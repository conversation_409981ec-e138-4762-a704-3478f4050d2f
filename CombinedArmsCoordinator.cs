using UnityEngine;
using System.Collections.Generic;
using System.Linq;

namespace ColdVor.RTS
{
    /// <summary>
    /// PERCEPTION SYSTEM: Analyzes combined arms groups and provides tactical data
    /// Does NOT control units - only provides analysis for AI decision making
    /// </summary>
    public class CombinedArmsCoordinator : SingletonBase<CombinedArmsCoordinator>
    {
        [Header("Coordination Settings")]
        [SerializeField] private float coordinationUpdateInterval = 1f;
        [SerializeField] private float maxCoordinationRange = 50f;
        [SerializeField] private bool enableCombinedArms = true;
        [SerializeField] private bool enableMutualSupport = true;

        [Header("Tactical Coordination")]
        [SerializeField] private float assaultSupportRange = 30f;
        [SerializeField] private float artillerySupportRange = 100f;
        [SerializeField] private float reconCoordinationRange = 40f;
        [SerializeField] private float commandCoordinationRange = 60f;

        // Active coordination groups
        private Dictionary<Faction, List<CombinedArmsGroup>> factionGroups = new Dictionary<Faction, List<CombinedArmsGroup>>();
        private Dictionary<Unit, CombinedArmsGroup> unitGroupAssignments = new Dictionary<Unit, CombinedArmsGroup>();
        private float lastCoordinationUpdate;

        // Events
        public System.Action<CombinedArmsGroup> OnGroupFormed;
        public System.Action<CombinedArmsGroup> OnGroupDisbanded;
        public System.Action<Unit, CombinedArmsOperation> OnOperationAssigned;

        protected override void OnSingletonAwake()
        {
            InitializeCoordination();
        }

        private void Update()
        {
            if (!enableCombinedArms) return;

            if (Time.time - lastCoordinationUpdate >= coordinationUpdateInterval)
            {
                UpdateCombinedArmsCoordination();
                lastCoordinationUpdate = Time.time;
            }
        }

        private void InitializeCoordination()
        {
            // Initialize faction groups
            foreach (Faction faction in System.Enum.GetValues(typeof(Faction)))
            {
                factionGroups[faction] = new List<CombinedArmsGroup>();
            }
        }

        private void UpdateCombinedArmsCoordination()
        {
            // Update existing groups
            foreach (var factionGroupList in factionGroups.Values)
            {
                for (int i = factionGroupList.Count - 1; i >= 0; i--)
                {
                    var group = factionGroupList[i];
                    UpdateGroup(group);
                    
                    // Remove disbanded groups
                    if (group.Units.Count == 0)
                    {
                        factionGroupList.RemoveAt(i);
                        OnGroupDisbanded?.Invoke(group);
                    }
                }
            }

            // Form new groups
            FormNewCombinedArmsGroups();
        }

        private void FormNewCombinedArmsGroups()
        {
            Unit[] allUnits = FindObjectsByType<Unit>(FindObjectsSortMode.None);
            
            foreach (Faction faction in System.Enum.GetValues(typeof(Faction)))
            {
                var factionUnits = allUnits.Where(u => u != null && u.Faction == faction && !u.IsDead && !IsUnitInGroup(u)).ToList();
                
                if (factionUnits.Count < 2) continue;

                // Group units by proximity and role compatibility
                var potentialGroups = IdentifyPotentialGroups(factionUnits);
                
                foreach (var potentialGroup in potentialGroups)
                {
                    if (ShouldFormGroup(potentialGroup))
                    {
                        CreateCombinedArmsGroup(potentialGroup, faction);
                    }
                }
            }
        }

        private List<List<Unit>> IdentifyPotentialGroups(List<Unit> units)
        {
            var groups = new List<List<Unit>>();
            var processed = new HashSet<Unit>();

            foreach (var unit in units)
            {
                if (processed.Contains(unit)) continue;

                var group = new List<Unit> { unit };
                processed.Add(unit);

                // Find nearby units that can coordinate
                foreach (var otherUnit in units)
                {
                    if (processed.Contains(otherUnit)) continue;

                    float distance = Vector3.Distance(unit.transform.position, otherUnit.transform.position);
                    if (distance <= maxCoordinationRange && CanUnitsCoordinate(unit, otherUnit))
                    {
                        group.Add(otherUnit);
                        processed.Add(otherUnit);
                    }
                }

                if (group.Count > 1)
                {
                    groups.Add(group);
                }
            }

            return groups;
        }

        private bool CanUnitsCoordinate(Unit unit1, Unit unit2)
        {
            var role1 = GetUnitRole(unit1);
            var role2 = GetUnitRole(unit2);

            if (role1 == null || role2 == null) return false;

            // Check distance based on role-specific coordination ranges
            float distance = Vector3.Distance(unit1.transform.position, unit2.transform.position);
            float maxRange = GetCoordinationRange(role1.RoleType, role2.RoleType);

            if (distance > maxRange) return false;

            // Check if roles can support each other
            return role1.SupportedRoles.Contains(role2.RoleType) ||
                   role2.SupportedRoles.Contains(role1.RoleType) ||
                   role1.SupportingRoles.Contains(role2.RoleType) ||
                   role2.SupportingRoles.Contains(role1.RoleType);
        }

        /// <summary>
        /// Get coordination range based on unit roles
        /// </summary>
        private float GetCoordinationRange(UnitRoleType role1, UnitRoleType role2)
        {
            // Use role-specific coordination ranges
            if (role1 == UnitRoleType.Artillery || role2 == UnitRoleType.Artillery)
                return artillerySupportRange;

            if (role1 == UnitRoleType.Recon || role2 == UnitRoleType.Recon)
                return reconCoordinationRange;

            if (role1 == UnitRoleType.Command || role2 == UnitRoleType.Command)
                return commandCoordinationRange;

            if (role1 == UnitRoleType.Assault || role2 == UnitRoleType.Assault)
                return assaultSupportRange;

            // Default coordination range
            return maxCoordinationRange;
        }

        private bool ShouldFormGroup(List<Unit> units)
        {
            if (units.Count < 2) return false;

            // Check for good role composition
            var roleTypes = units.Select(u => GetUnitRole(u)?.RoleType ?? UnitRoleType.Assault).ToList();
            
            // Prefer groups with complementary roles
            bool hasAssault = roleTypes.Contains(UnitRoleType.Assault);
            bool hasSupport = roleTypes.Contains(UnitRoleType.Support) || roleTypes.Contains(UnitRoleType.Artillery);
            bool hasRecon = roleTypes.Contains(UnitRoleType.Recon);

            // Good combined arms composition
            return (hasAssault && hasSupport) || (hasAssault && hasRecon) || roleTypes.Distinct().Count() > 1;
        }

        private void CreateCombinedArmsGroup(List<Unit> units, Faction faction)
        {
            var group = new CombinedArmsGroup
            {
                Id = System.Guid.NewGuid().ToString(),
                Faction = faction,
                Units = new List<Unit>(units),
                FormationTime = Time.time,
                CurrentOperation = CombinedArmsOperation.None
            };

            // Assign group roles
            AssignGroupRoles(group);

            // Add to faction groups
            factionGroups[faction].Add(group);

            // Track unit assignments
            foreach (var unit in units)
            {
                unitGroupAssignments[unit] = group;
            }

            OnGroupFormed?.Invoke(group);
        }

        private void AssignGroupRoles(CombinedArmsGroup group)
        {
            group.AssaultUnits.Clear();
            group.SupportUnits.Clear();
            group.ReconUnits.Clear();
            group.SpecialUnits.Clear();

            foreach (var unit in group.Units)
            {
                var role = GetUnitRole(unit);
                if (role == null) continue;

                switch (role.RoleType)
                {
                    case UnitRoleType.Assault:
                        group.AssaultUnits.Add(unit);
                        break;
                    case UnitRoleType.Support:
                    case UnitRoleType.Artillery:
                    case UnitRoleType.AntiAir:
                        group.SupportUnits.Add(unit);
                        break;
                    case UnitRoleType.Recon:
                        group.ReconUnits.Add(unit);
                        break;
                    default:
                        group.SpecialUnits.Add(unit);
                        break;
                }
            }

            // Assign group leader (highest ranking unit or command unit)
            group.Leader = DetermineGroupLeader(group.Units);
        }

        private Unit DetermineGroupLeader(List<Unit> units)
        {
            // Prefer command units
            var commandUnit = units.FirstOrDefault(u => GetUnitRole(u)?.RoleType == UnitRoleType.Command);
            if (commandUnit != null) return commandUnit;

            // Otherwise, use highest ranking unit from CommandHierarchy
            Unit bestLeader = units[0];
            CommandRank bestRank = CommandRank.Private;

            foreach (var unit in units)
            {
                if (CommandHierarchy.Instance != null)
                {
                    var rank = CommandHierarchy.Instance.GetUnitRank(unit);
                    if (rank > bestRank)
                    {
                        bestRank = rank;
                        bestLeader = unit;
                    }
                }
            }

            return bestLeader;
        }

        private void UpdateGroup(CombinedArmsGroup group)
        {
            // Remove dead or invalid units
            group.Units.RemoveAll(u => u == null || u.IsDead);
            
            if (group.Units.Count == 0) return;

            // Update group roles
            AssignGroupRoles(group);

            // Analyze group situation (no direct control)
            if (enableMutualSupport)
            {
                AnalyzeGroupSituation(group);
            }
        }

        private void AnalyzeGroupSituation(CombinedArmsGroup group)
        {
            // Analyze current tactical situation (no execution)
            var threats = AnalyzeGroupThreats(group);
            var recommendedOperation = DetermineOptimalOperation(group, threats);

            // Store recommendation for AI to read - don't execute
            group.RecommendedOperation = recommendedOperation;
            group.LastAnalysisTime = Time.time;
        }

        private List<Unit> AnalyzeGroupThreats(CombinedArmsGroup group)
        {
            var threats = new List<Unit>();
            
            foreach (var unit in group.Units)
            {
                threats.AddRange(unit.DetectedEnemies);
            }

            return threats.Distinct().ToList();
        }

        private CombinedArmsOperation DetermineOptimalOperation(CombinedArmsGroup group, List<Unit> threats)
        {
            if (threats.Count == 0) return CombinedArmsOperation.Patrol;

            // Analyze threat composition and group capabilities
            bool hasAssaultCapability = group.AssaultUnits.Count > 0;
            bool hasFireSupport = group.SupportUnits.Count > 0;
            bool hasRecon = group.ReconUnits.Count > 0;

            float groupStrength = CalculateGroupStrength(group);
            float threatStrength = CalculateThreatStrength(threats);

            if (groupStrength > threatStrength * 1.5f && hasAssaultCapability)
            {
                return CombinedArmsOperation.Assault;
            }
            else if (hasFireSupport && hasAssaultCapability)
            {
                return CombinedArmsOperation.SupportedAdvance;
            }
            else if (hasRecon)
            {
                return CombinedArmsOperation.Reconnaissance;
            }
            else
            {
                return CombinedArmsOperation.Defense;
            }
        }

        private float CalculateGroupStrength(CombinedArmsGroup group)
        {
            float strength = 0f;
            foreach (var unit in group.Units)
            {
                strength += unit.CurrentHealth + unit.WeaponDamage;
            }
            return strength;
        }

        private float CalculateThreatStrength(List<Unit> threats)
        {
            float strength = 0f;
            foreach (var threat in threats)
            {
                strength += threat.CurrentHealth + threat.WeaponDamage;
            }
            return strength;
        }

        // REMOVED: No execution methods - this is a perception system only
        // AI controllers read group recommendations and make their own decisions

        // Public API
        public CombinedArmsGroup GetUnitGroup(Unit unit)
        {
            return unitGroupAssignments.ContainsKey(unit) ? unitGroupAssignments[unit] : null;
        }

        public bool IsUnitInGroup(Unit unit)
        {
            return unitGroupAssignments.ContainsKey(unit);
        }

        public UnitRole GetUnitRole(Unit unit)
        {
            var roleComponent = unit.GetComponent<UnitRoleComponent>();
            return roleComponent?.Role;
        }

        public List<CombinedArmsGroup> GetFactionGroups(Faction faction)
        {
            return factionGroups.ContainsKey(faction) ? new List<CombinedArmsGroup>(factionGroups[faction]) : new List<CombinedArmsGroup>();
        }

        /// <summary>
        /// Get the recommended operation for a unit's group (for AI to read)
        /// </summary>
        public CombinedArmsOperation GetGroupRecommendation(Unit unit)
        {
            var group = GetUnitGroup(unit);
            if (group != null && Time.time - group.LastAnalysisTime < 5f) // Recent analysis
            {
                return group.RecommendedOperation;
            }
            return CombinedArmsOperation.None;
        }
    }

    /// <summary>
    /// Combined arms group data structure
    /// </summary>
    [System.Serializable]
    public class CombinedArmsGroup
    {
        public string Id;
        public Faction Faction;
        public List<Unit> Units = new List<Unit>();
        public Unit Leader;
        public float FormationTime;
        public CombinedArmsOperation CurrentOperation;
        public CombinedArmsOperation RecommendedOperation;
        public float LastAnalysisTime;

        // Role-based unit lists
        public List<Unit> AssaultUnits = new List<Unit>();
        public List<Unit> SupportUnits = new List<Unit>();
        public List<Unit> ReconUnits = new List<Unit>();
        public List<Unit> SpecialUnits = new List<Unit>();

        public Vector3 GroupCenter
        {
            get
            {
                if (Units.Count == 0) return Vector3.zero;
                Vector3 center = Vector3.zero;
                foreach (var unit in Units)
                {
                    center += unit.transform.position;
                }
                return center / Units.Count;
            }
        }
    }

    /// <summary>
    /// Types of combined arms operations
    /// </summary>
    public enum CombinedArmsOperation
    {
        None,
        Patrol,
        Assault,
        Defense,
        SupportedAdvance,
        Reconnaissance,
        Withdrawal,
        Regroup
    }
}
