using UnityEngine;

namespace ColdVor.RTS
{
    /// <summary>
    /// Handles projectile movement, collision detection, and damage application
    /// Supports ballistic trajectories, homing projectiles, and explosive warheads
    /// </summary>
    [RequireComponent(typeof(Rigidbody))]
    public class Projectile : MonoBehaviour
    {
        [Header("Projectile Settings")]
        [SerializeField] private float lifetime = 5f; // Only non-weapon parameter that stays

    [Header("Collision Settings")]
    [SerializeField] private LayerMask collisionLayerMask = (1 << 3); // Only hit units on layer 3

        [Header("Visual")]
        [SerializeField] private TrailRenderer trailRenderer;
        [SerializeField] private Light projectileLight;

        // Projectile data - only runtime state, no weapon parameters
        private Unit attacker;
        private Unit target;
        private Vector3 targetPosition;
        private Rigidbody rb;
        private float spawnTime;
        private bool hasHit = false;
        private float collisionDelay = 0.01f; // Minimal delay to avoid hitting the shooter
        private bool needsVisualSetup = false;

        // Weapon parameters received from WeaponSystem (not stored in projectile)
        private float speed;
        private bool useGravity;
        private bool isHoming;
        private float homingStrength;
        private WeaponType weaponType;
        private int damage;
        private bool isExplosive;
        private float explosionRadius;

        // Ballistics
        private Vector3 initialVelocity;
        private float gravityScale = 1f;

        private void Awake()
        {
            rb = GetComponent<Rigidbody>();
            spawnTime = Time.time;

            // Cache trail renderer and light references if they exist
            if (trailRenderer == null)
            {
                trailRenderer = GetComponent<TrailRenderer>();
            }
            if (projectileLight == null)
            {
                projectileLight = GetComponent<Light>();
            }
        }

        private void Start()
        {
            // Set up visual effects if they were deferred
            if (needsVisualSetup)
            {
                SetupVisualEffects();
                needsVisualSetup = false;
            }

            // Destroy projectile after lifetime
            Destroy(gameObject, lifetime);
        }

        private void Update()
        {
            if (hasHit) return;

            // Update homing behavior
            if (isHoming && target != null && target.CurrentHealth > 0)
            {
                UpdateHoming();
            }

            // Check for manual collision (in case Rigidbody collision fails)
            CheckManualCollision();
        }

        private void FixedUpdate()
        {
            if (hasHit) return;

            // Apply custom gravity if needed
            if (useGravity && gravityScale != 1f)
            {
                rb.AddForce(Physics.gravity * (gravityScale - 1f), ForceMode.Acceleration);
            }
        }

        /// <summary>
        /// Initialize the projectile with target and weapon parameters from WeaponSystem
        /// </summary>
        public void Initialize(Unit attacker, Unit target, Vector3 targetPosition, ProjectileParameters parameters)
        {
            this.attacker = attacker;
            this.target = target;
            this.targetPosition = targetPosition;

            // Receive weapon parameters from WeaponSystem (single source of truth)
            this.speed = parameters.speed;
            this.useGravity = parameters.useGravity;
            this.isHoming = parameters.isHoming;
            this.homingStrength = parameters.homingStrength;
            this.weaponType = parameters.weaponType;
            this.damage = parameters.damage;
            this.isExplosive = parameters.isExplosive;
            this.explosionRadius = parameters.explosionRadius;

            // Calculate initial velocity
            CalculateTrajectory();

            // Set up visual effects (defer coroutines until Start if inactive)
            if (gameObject.activeInHierarchy)
            {
                SetupVisualEffects();
            }
            else
            {
                // Mark that we need to setup visual effects when activated
                needsVisualSetup = true;
            }
        }

        private void CalculateTrajectory()
        {
            // Ensure Rigidbody is available
            if (rb == null)
            {
                rb = GetComponent<Rigidbody>();
                if (rb == null)
                {
                    Debug.LogError("Projectile.CalculateTrajectory: No Rigidbody found! Adding one.");
                    rb = gameObject.AddComponent<Rigidbody>();
                    rb.useGravity = false;
                    rb.linearDamping = 0f;
                }
            }

            Vector3 direction = (targetPosition - transform.position).normalized;

            if (useGravity && !isHoming)
            {
                // Calculate ballistic trajectory
                initialVelocity = CalculateBallisticVelocity(transform.position, targetPosition, speed);
                rb.linearVelocity = initialVelocity;
            }
            else
            {
                // Direct trajectory
                rb.linearVelocity = direction * speed;
            }

            // Orient projectile towards target
            if (rb.linearVelocity != Vector3.zero)
            {
                transform.rotation = Quaternion.LookRotation(rb.linearVelocity.normalized);
            }


        }

        private Vector3 CalculateBallisticVelocity(Vector3 start, Vector3 target, float speed)
        {
            Vector3 displacement = target - start;
            Vector3 horizontalDisplacement = new Vector3(displacement.x, 0, displacement.z);
            float horizontalDistance = horizontalDisplacement.magnitude;
            float verticalDistance = displacement.y;

            // Calculate time of flight for given speed
            float timeOfFlight = horizontalDistance / speed;

            // Calculate required vertical velocity to reach target
            float verticalVelocity = (verticalDistance / timeOfFlight) + (0.5f * Mathf.Abs(Physics.gravity.y) * timeOfFlight);

            Vector3 horizontalVelocity = horizontalDisplacement.normalized * speed;
            return horizontalVelocity + Vector3.up * verticalVelocity;
        }

        private void UpdateHoming()
        {
            if (target == null || target.CurrentHealth <= 0) return;

            Vector3 targetDirection = (target.transform.position - transform.position).normalized;
            Vector3 currentDirection = rb.linearVelocity.normalized;

            // Gradually turn towards target
            Vector3 newDirection = Vector3.Slerp(currentDirection, targetDirection, homingStrength * Time.deltaTime);
            rb.linearVelocity = newDirection * speed;

            // Update rotation
            transform.rotation = Quaternion.LookRotation(rb.linearVelocity.normalized);
        }

        private void CheckManualCollision()
        {
            // Don't check collision immediately after spawning
            if (Time.time - spawnTime < collisionDelay) return;

            // Cast a ray from previous position to current position
            Vector3 currentPosition = transform.position;
            Vector3 previousPosition = currentPosition - rb.linearVelocity * Time.deltaTime;

            float distance = Vector3.Distance(previousPosition, currentPosition);
            Vector3 direction = (currentPosition - previousPosition).normalized;

            // Use layer mask to exclude terrain and other unwanted layers
            if (Physics.Raycast(previousPosition, direction, out RaycastHit hit, distance, collisionLayerMask))
            {
                // Don't hit the attacker
                Unit hitUnit = hit.collider.GetComponent<Unit>();
                if (hitUnit == attacker) return;

                ProcessHit(hit.point, hit.normal, hit.collider);
            }
        }

        private void OnTriggerEnter(Collider other)
        {
            if (hasHit) return;

            // Check if the collider is on a layer we should hit
            if (!RTSUtilities.IsLayerInMask(other.gameObject.layer, collisionLayerMask)) return;

            // Don't hit the attacker
            Unit hitUnit = RTSUtilities.GetUnitFromCollider(other);
            if (hitUnit == attacker) return;

            // Don't collide immediately after spawning (only for non-target units)
            if (Time.time - spawnTime < collisionDelay && hitUnit != target) return;

            // Handle the hit directly without reflection
            hasHit = true;
            OnHitTrigger(other);
        }

        private void OnHitTrigger(Collider other)
        {
            // Calculate hit point as closest point on collider to projectile
            Vector3 hitPoint = other.ClosestPoint(transform.position);

            // Get the unit that was hit
            Unit hitUnit = RTSUtilities.GetUnitFromCollider(other);

            if (isExplosive)
            {
                // Create explosion
                if (CombatSystem.Instance != null)
                {
                    CombatSystem.Instance.CreateExplosion(hitPoint, explosionRadius, damage, attacker);
                }
            }
            else if (hitUnit != null)
            {
                // Direct hit on unit
                if (CombatSystem.Instance != null)
                {
                    CombatSystem.Instance.ApplyDamage(attacker, hitUnit, damage, hitPoint);
                }
            }

            // Remove from combat system tracking
            if (CombatSystem.Instance != null)
            {
                CombatSystem.Instance.RemoveProjectile(this);
            }

            // Destroy projectile
            DestroyProjectile();
        }

        private void OnCollisionEnter(Collision collision)
        {
            if (hasHit) return;

            // Check if the collider is on a layer we should hit
            if (!RTSUtilities.IsLayerInMask(collision.gameObject.layer, collisionLayerMask)) return;

            // Handle collision hit directly without reflection
            HandleCollisionHit(collision);
        }

        private void HandleCollisionHit(Collision collision)
        {
            Vector3 hitPoint = transform.position;
            Vector3 normal = -transform.forward;

            if (collision.contacts.Length > 0)
            {
                // Use the first contact point for accurate hit data
                ContactPoint contact = collision.contacts[0];
                hitPoint = contact.point;
                normal = contact.normal;
            }

            // Process the hit directly without using RaycastHit
            ProcessHit(hitPoint, normal, collision.collider);
        }

        private void ProcessHit(Vector3 hitPoint, Vector3 normal, Collider hitCollider)
        {
            if (hasHit) return;
            hasHit = true;

            // Disable further collision detection immediately
            Collider projectileCollider = GetComponent<Collider>();
            if (projectileCollider != null)
            {
                projectileCollider.enabled = false;
            }

            // Use the provided hit point, or fallback to transform position
            if (hitPoint == Vector3.zero) hitPoint = transform.position;

            // Check what we hit
            Unit hitUnit = hitCollider?.GetComponent<Unit>();

            if (isExplosive)
            {
                // Create explosion
                if (CombatSystem.Instance != null)
                {
                    CombatSystem.Instance.CreateExplosion(hitPoint, explosionRadius, damage, attacker);
                }


            }
            else if (hitUnit != null)
            {
                // Direct hit on unit
                if (CombatSystem.Instance != null)
                {
                    CombatSystem.Instance.ApplyDamage(attacker, hitUnit, damage, hitPoint);
                }

            }

            // Remove from combat system tracking
            if (CombatSystem.Instance != null)
            {
                CombatSystem.Instance.RemoveProjectile(this);
            }

            // Destroy projectile
            DestroyProjectile();
        }

        private void SetupVisualEffects()
        {
            // Use trail renderer if available, otherwise create custom tracer
            if (trailRenderer != null)
            {
                // Configure the trail renderer properly
                trailRenderer.enabled = true;
                trailRenderer.material.color = GetTrailColor();
                trailRenderer.startWidth = 0.05f;
                trailRenderer.endWidth = 0.01f;
                trailRenderer.time = 0.2f;
                trailRenderer.minVertexDistance = 0.1f;
            }
            else
            {
                // Create custom tracer effect for fallback projectiles
                CreateCustomTracer();
            }

            // Set up projectile light if present
            if (projectileLight != null)
            {
                projectileLight.color = GetTrailColor();
                projectileLight.intensity = 2f;
                projectileLight.range = 5f;
                projectileLight.enabled = true;
            }

            // Set projectile color and emission based on weapon type
            Renderer renderer = GetComponent<Renderer>();
            if (renderer != null)
            {
                // Create a new material instance to avoid shared material issues
                Material mat = new Material(renderer.material);
                mat.color = GetTrailColor();

                // Make all projectiles glow for better visibility
                mat.EnableKeyword("_EMISSION");
                mat.SetColor("_EmissionColor", GetTrailColor() * 1.5f);

                // Extra glow for energy weapons
                if (weaponType == WeaponType.Energy)
                {
                    mat.SetColor("_EmissionColor", GetTrailColor() * 3f);
                }

                renderer.material = mat;
            }
        }

        private Color GetTrailColor()
        {
            switch (weaponType)
            {
                case WeaponType.Ballistic:
                    return Color.yellow;
                case WeaponType.Energy:
                    return Color.cyan;
                case WeaponType.Explosive:
                    return Color.red;
                default:
                    return Color.white;
            }
        }

        private void DestroyProjectile()
        {
            // Return to pool instead of destroying
            if (CombatSystem.Instance != null)
            {
                CombatSystem.Instance.ReturnProjectileToPool(gameObject);
            }
            else
            {
                // Fallback to destruction if no combat system
                Destroy(gameObject);
            }
        }

        /// <summary>
        /// Reset projectile state for object pooling - comprehensive reset to prevent leaks and state corruption
        /// </summary>
        public void ResetProjectile()
        {
            // Stop all coroutines to prevent memory leaks and continued execution
            StopAllCoroutines();

            // Reset core state
            hasHit = false;
            spawnTime = Time.time;
            needsVisualSetup = false;

            // Clear references to prevent memory leaks
            attacker = null;
            target = null;

            // Reset weapon parameters
            speed = 0f;
            useGravity = false;
            isHoming = false;
            homingStrength = 0f;
            weaponType = WeaponType.Ballistic;
            damage = 0;
            isExplosive = false;
            explosionRadius = 0f;

            // Reset transform
            transform.rotation = Quaternion.identity;
            transform.localScale = Vector3.one;

            // Ensure Rigidbody is available and reset
            if (rb == null)
            {
                rb = GetComponent<Rigidbody>();
                if (rb == null)
                {
                    rb = gameObject.AddComponent<Rigidbody>();
                }
            }

            // Reset rigidbody completely
            rb.linearVelocity = Vector3.zero;
            rb.angularVelocity = Vector3.zero;
            rb.useGravity = false;
            rb.linearDamping = 0f;
            rb.angularDamping = 0f;

            // Re-enable colliders
            Collider[] colliders = GetComponents<Collider>();
            foreach (Collider col in colliders)
            {
                if (col != null)
                {
                    col.enabled = true;
                    col.isTrigger = true; // Ensure proper collision detection
                }
            }

            // Reset visual components safely
            ResetVisualComponents();

            // Clean up any custom objects created during projectile lifetime
            CleanupCustomObjects();

            // Reset material instances to prevent memory leaks
            CleanupMaterialInstances();
        }

        /// <summary>
        /// Reset visual components safely
        /// </summary>
        private void ResetVisualComponents()
        {
            // Reset trail renderer
            if (trailRenderer != null)
            {
                trailRenderer.Clear();
                trailRenderer.enabled = true;
                // Reset trail properties to defaults
                trailRenderer.time = 0.2f;
                trailRenderer.startWidth = 0.05f;
                trailRenderer.endWidth = 0.01f;
            }

            // Reset light component
            if (projectileLight != null)
            {
                projectileLight.enabled = true;
                projectileLight.intensity = 2f;
                projectileLight.range = 5f;
            }

            // Reset renderer
            Renderer renderer = GetComponent<Renderer>();
            if (renderer != null)
            {
                renderer.enabled = true;
            }
        }

        /// <summary>
        /// Clean up custom objects created during projectile lifetime
        /// </summary>
        private void CleanupCustomObjects()
        {
            // Clean up custom tracer objects
            Transform customTracer = transform.Find("CustomTracer");
            if (customTracer != null)
            {
                DestroyImmediate(customTracer.gameObject);
            }

            // Clean up any other child objects that might have been created
            for (int i = transform.childCount - 1; i >= 0; i--)
            {
                Transform child = transform.GetChild(i);
                if (child.name.Contains("Custom") || child.name.Contains("Effect"))
                {
                    DestroyImmediate(child.gameObject);
                }
            }
        }

        /// <summary>
        /// Clean up material instances to prevent memory leaks
        /// </summary>
        private void CleanupMaterialInstances()
        {
            Renderer renderer = GetComponent<Renderer>();
            if (renderer != null && renderer.material != null)
            {
                // Only destroy material instances, not shared materials
                if (renderer.material.name.Contains("(Instance)"))
                {
                    Material instanceMaterial = renderer.material;

                    // Reset to a default material to avoid null references
                    renderer.material = new Material(Shader.Find("Standard"));

                    // Destroy the instance material
                    DestroyImmediate(instanceMaterial);
                }
            }

            // Also clean up trail renderer materials if they're instances
            if (trailRenderer != null && trailRenderer.material != null)
            {
                if (trailRenderer.material.name.Contains("(Instance)"))
                {
                    Material instanceMaterial = trailRenderer.material;
                    trailRenderer.material = new Material(Shader.Find("Sprites/Default"));
                    DestroyImmediate(instanceMaterial);
                }
            }
        }

        private System.Collections.IEnumerator QuickFadeAndDestroy()
        {
            // Get all renderers for fading
            Renderer[] renderers = GetComponentsInChildren<Renderer>();
            Light[] lights = GetComponentsInChildren<Light>();

            // Store original values
            Color[] originalColors = new Color[renderers.Length];
            float[] originalIntensities = new float[lights.Length];

            for (int i = 0; i < renderers.Length; i++)
            {
                if (renderers[i] != null && renderers[i].material != null)
                    originalColors[i] = renderers[i].material.color;
            }

            for (int i = 0; i < lights.Length; i++)
            {
                if (lights[i] != null)
                    originalIntensities[i] = lights[i].intensity;
            }

            // Quick fade over 0.1 seconds
            float fadeTime = 0.1f;
            float elapsed = 0f;

            while (elapsed < fadeTime)
            {
                elapsed += Time.deltaTime;
                float alpha = Mathf.Lerp(1f, 0f, elapsed / fadeTime);

                // Fade renderers
                for (int i = 0; i < renderers.Length; i++)
                {
                    if (renderers[i] != null && renderers[i].material != null)
                    {
                        Color color = originalColors[i];
                        renderers[i].material.color = new Color(color.r, color.g, color.b, alpha);
                    }
                }

                // Fade lights
                for (int i = 0; i < lights.Length; i++)
                {
                    if (lights[i] != null)
                    {
                        lights[i].intensity = originalIntensities[i] * alpha;
                    }
                }

                yield return null;
            }

            // Destroy after fade
            Destroy(gameObject);
        }

        /// <summary>
        /// Get the remaining lifetime of the projectile
        /// </summary>
        public float GetRemainingLifetime()
        {
            return lifetime - (Time.time - spawnTime);
        }

        /// <summary>
        /// Check if the projectile is still active
        /// </summary>
        public bool IsActive()
        {
            return !hasHit && GetRemainingLifetime() > 0;
        }

        /// <summary>
        /// Manually detonate the projectile (for explosive projectiles)
        /// </summary>
        public void Detonate()
        {
            if (hasHit) return;

            if (isExplosive && CombatSystem.Instance != null)
            {
                CombatSystem.Instance.CreateExplosion(transform.position, explosionRadius, damage, attacker);
            }

            hasHit = true;
            DestroyProjectile();
        }

        private void CreateCustomTracer()
        {
            // Create a tiny glowing dot instead of a line - like a real tracer
            GameObject tracerObj = new GameObject("CustomTracer");
            tracerObj.transform.SetParent(transform);
            tracerObj.transform.localPosition = Vector3.back * 0.08f; // A bit further behind projectile

            // Create a small sphere for the tracer dot
            MeshRenderer renderer = tracerObj.AddComponent<MeshRenderer>();
            MeshFilter filter = tracerObj.AddComponent<MeshFilter>();

            // Use a simple quad mesh
            filter.mesh = CreateQuadMesh();

            // Create bright glowing material using Standard shader for proper lighting
            Material tracerMaterial = new Material(Shader.Find("Standard"));
            tracerMaterial.color = GetTrailColor();
            tracerMaterial.EnableKeyword("_EMISSION");
            tracerMaterial.SetColor("_EmissionColor", GetTrailColor() * 3.0f); // Very bright emission
            tracerMaterial.SetFloat("_Metallic", 0f);
            tracerMaterial.SetFloat("_Smoothness", 1f);
            renderer.material = tracerMaterial;

            // Scale it small but visible
            tracerObj.transform.localScale = Vector3.one * 0.022f;

            // Start the tracer update coroutine
            StartCoroutine(UpdateDotTracer(tracerObj, tracerMaterial));
        }

        private Mesh CreateQuadMesh()
        {
            Mesh mesh = new Mesh();
            mesh.vertices = new Vector3[]
            {
                new Vector3(-0.5f, -0.5f, 0),
                new Vector3(0.5f, -0.5f, 0),
                new Vector3(-0.5f, 0.5f, 0),
                new Vector3(0.5f, 0.5f, 0)
            };
            mesh.triangles = new int[] { 0, 2, 1, 2, 3, 1 };
            mesh.uv = new Vector2[]
            {
                new Vector2(0, 0),
                new Vector2(1, 0),
                new Vector2(0, 1),
                new Vector2(1, 1)
            };
            mesh.RecalculateNormals();
            return mesh;
        }

        private System.Collections.IEnumerator UpdateDotTracer(GameObject tracerObj, Material tracerMaterial)
        {
            while (!hasHit && tracerObj != null)
            {
                // The dot just follows the projectile as a child - no line needed!
                yield return null;
            }

            // Fade out the tracer when projectile hits
            if (tracerObj != null)
            {
                StartCoroutine(FadeOutDotTracer(tracerObj, tracerMaterial));
            }
        }

        private System.Collections.IEnumerator FadeOutDotTracer(GameObject tracerObj, Material tracerMaterial)
        {
            Color originalColor = tracerMaterial.color;
            float fadeTime = 0.01f; // Super quick fade - almost instant
            float elapsed = 0f;

            while (elapsed < fadeTime && tracerObj != null && tracerMaterial != null)
            {
                elapsed += Time.deltaTime;
                float alpha = Mathf.Lerp(1f, 0f, elapsed / fadeTime);
                tracerMaterial.color = new Color(originalColor.r, originalColor.g, originalColor.b, alpha);
                yield return null;
            }

            if (tracerObj != null)
            {
                Destroy(tracerObj);
            }
        }
    }

    /// <summary>
    /// Parameters passed from WeaponSystem to Projectile - single source of truth
    /// </summary>
    public struct ProjectileParameters
    {
        public float speed;
        public bool useGravity;
        public bool isHoming;
        public float homingStrength;
        public WeaponType weaponType;
        public int damage;
        public bool isExplosive;
        public float explosionRadius;
        public float accuracy;

        // FromWeaponData method removed - use Weapon.ToProjectileParameters() instead

        /// <summary>
        /// Create ProjectileParameters from Weapon ScriptableObject
        /// </summary>
        public static ProjectileParameters FromWeapon(Weapon weapon)
        {
            return new ProjectileParameters
            {
                speed = weapon.ProjectileSpeed,
                useGravity = weapon.UseGravity,
                isHoming = weapon.IsHoming,
                homingStrength = 2f, // Could be added to Weapon if needed
                weaponType = weapon.WeaponType,
                damage = weapon.Damage,
                isExplosive = weapon.IsExplosive,
                explosionRadius = weapon.ExplosionRadius,
                accuracy = weapon.Accuracy
            };
        }
    }
}
