using UnityEngine;
using UnityEngine.AI;
using System;

namespace ColdVor.RTS
{
    /// <summary>
    /// Dedicated movement system component for unit navigation and positioning.
    /// Handles all movement-related functionality separate from other systems.
    /// </summary>
    public class MovementSystem : MonoBehaviour, IMovementProvider
    {
        [Header("Movement Configuration")]
        [SerializeField] private float moveSpeed = 5f;
        [SerializeField] private float rotationSpeed = 120f;
        [SerializeField] private float stoppingDistance = 0.5f;
        [SerializeField] private float acceleration = 8f;
        
        [Header("Formation Settings")]
        [SerializeField] private bool maintainFormation = false;
        [SerializeField] private Vector3 formationOffset = Vector3.zero;
        [SerializeField] private float formationTolerance = 2f;
        
        // Movement state
        private Vector3 destination;
        private bool hasDestination = false;
        private bool isMoving = false;
        private Vector3 lastPosition;
        private float lastMoveTime;
        
        // Cached components
        private NavMeshAgent navAgent;
        private Unit ownerUnit;
        
        // Events
        public event Action<MovementSystem> OnMovementStarted;
        public event Action<MovementSystem> OnMovementStopped;
        public event Action<MovementSystem, Vector3> OnDestinationReached;
        public event Action<MovementSystem> OnMovementBlocked;
        
        // Properties - IMovementProvider implementation
        public bool IsMoving => isMoving;
        public Vector3 Velocity => navAgent?.velocity ?? Vector3.zero;
        public Vector3 Destination => destination;
        public float MovementSpeed => moveSpeed;
        
        // Additional properties
        public bool HasDestination => hasDestination;
        public float DistanceToDestination => hasDestination ? Vector3.Distance(transform.position, destination) : 0f;
        public bool IsAtDestination => hasDestination && DistanceToDestination <= stoppingDistance;
        
        private void Awake()
        {
            ownerUnit = GetComponent<Unit>();
            navAgent = GetComponent<NavMeshAgent>();

            if (navAgent == null)
            {
                navAgent = gameObject.AddComponent<NavMeshAgent>();
            }

            InitializeNavAgent();
        }

        private void Start()
        {
            // Sync with Unit's movement settings if available
            if (ownerUnit != null)
            {
                // Use Unit's configured speed if different
                if (ownerUnit.NavAgent != null && ownerUnit.NavAgent.speed != moveSpeed)
                {
                    moveSpeed = ownerUnit.NavAgent.speed;
                    navAgent.speed = moveSpeed;
                }
            }

            lastPosition = transform.position;
        }
        
        private void Update()
        {
            UpdateMovementState();
            CheckDestinationReached();
            UpdateFormationPosition();
        }
        
        /// <summary>
        /// Initialize NavMeshAgent with movement settings and collision avoidance
        /// </summary>
        private void InitializeNavAgent()
        {
            if (navAgent == null) return;

            navAgent.speed = moveSpeed;
            navAgent.angularSpeed = rotationSpeed;
            navAgent.stoppingDistance = stoppingDistance;
            navAgent.acceleration = acceleration;
            navAgent.autoBraking = true;
            navAgent.autoRepath = true;

            // CRITICAL: Collision avoidance settings
            navAgent.obstacleAvoidanceType = ObstacleAvoidanceType.HighQualityObstacleAvoidance;
            navAgent.avoidancePriority = UnityEngine.Random.Range(30, 70); // Randomize to prevent deadlocks
            navAgent.radius = 0.8f; // Ensure units don't overlap
            navAgent.height = 2f; // Standard unit height

            // Enable proper collision detection
            Collider unitCollider = GetComponent<Collider>();
            if (unitCollider != null)
            {
                // Ensure collider radius matches NavMeshAgent radius
                if (unitCollider is CapsuleCollider capsule)
                {
                    navAgent.radius = Mathf.Max(capsule.radius, 0.8f);
                    navAgent.height = capsule.height;
                }
                else if (unitCollider is SphereCollider sphere)
                {
                    navAgent.radius = Mathf.Max(sphere.radius, 0.8f);
                }
            }
        }
        
        /// <summary>
        /// Update movement state tracking
        /// </summary>
        private void UpdateMovementState()
        {
            bool wasMoving = isMoving;
            
            // Check if actually moving
            float distanceMoved = Vector3.Distance(transform.position, lastPosition);
            bool actuallyMoving = distanceMoved > 0.01f && navAgent != null && navAgent.velocity.magnitude > 0.1f;
            
            isMoving = actuallyMoving && hasDestination;
            
            // Trigger events on state change
            if (isMoving && !wasMoving)
            {
                OnMovementStarted?.Invoke(this);
                MovementSystemEvents.TriggerMovementStarted(this);
            }
            else if (!isMoving && wasMoving)
            {
                OnMovementStopped?.Invoke(this);
                MovementSystemEvents.TriggerMovementStopped(this);
            }
            
            lastPosition = transform.position;
            
            // Check for movement blocking
            if (hasDestination && !isMoving && Time.time - lastMoveTime > 2f)
            {
                OnMovementBlocked?.Invoke(this);
                MovementSystemEvents.TriggerMovementBlocked(this);
            }
            
            if (isMoving)
            {
                lastMoveTime = Time.time;
            }
        }
        
        /// <summary>
        /// Check if destination has been reached
        /// </summary>
        private void CheckDestinationReached()
        {
            if (!hasDestination) return;
            
            if (IsAtDestination || (navAgent != null && !navAgent.pathPending && navAgent.remainingDistance < stoppingDistance))
            {
                ReachDestination();
            }
        }
        
        /// <summary>
        /// Handle reaching destination
        /// </summary>
        private void ReachDestination()
        {
            Vector3 reachedDestination = destination;
            hasDestination = false;
            isMoving = false;
            
            OnDestinationReached?.Invoke(this, reachedDestination);
            MovementSystemEvents.TriggerDestinationReached(this, reachedDestination);
            
            if (DebugManager.Instance != null)
            {
                DebugManager.Instance.LogSystem("MovementSystem", $"{gameObject.name} reached destination");
            }
        }
        
        /// <summary>
        /// Update formation positioning if enabled
        /// </summary>
        private void UpdateFormationPosition()
        {
            if (!maintainFormation || formationOffset == Vector3.zero) return;
            
            // Formation logic would go here - simplified for now
            Vector3 targetFormationPosition = destination + formationOffset;
            float distanceFromFormation = Vector3.Distance(transform.position, targetFormationPosition);
            
            if (distanceFromFormation > formationTolerance)
            {
                // Adjust movement to maintain formation
                MoveTo(targetFormationPosition);
            }
        }
        
        /// <summary>
        /// Move to a specific destination
        /// </summary>
        public bool MoveTo(Vector3 targetDestination)
        {
            if (navAgent == null || !navAgent.isOnNavMesh)
            {
                Debug.LogWarning($"MovementSystem.MoveTo: NavMeshAgent not ready on {gameObject.name}");
                return false;
            }
            
            destination = targetDestination;
            hasDestination = true;
            
            bool pathSet = navAgent.SetDestination(destination);
            
            if (pathSet)
            {
                if (DebugManager.Instance != null)
                {
                    DebugManager.Instance.LogSystem("MovementSystem", $"{gameObject.name} moving to {destination}");
                }
            }
            else
            {
                Debug.LogWarning($"MovementSystem.MoveTo: Failed to set destination for {gameObject.name}");
                hasDestination = false;
            }
            
            return pathSet;
        }
        
        /// <summary>
        /// Stop current movement
        /// </summary>
        public void Stop()
        {
            if (navAgent != null)
            {
                navAgent.ResetPath();
            }
            
            hasDestination = false;
            isMoving = false;
            
            OnMovementStopped?.Invoke(this);
            MovementSystemEvents.TriggerMovementStopped(this);
            
            if (DebugManager.Instance != null)
            {
                DebugManager.Instance.LogSystem("MovementSystem", $"{gameObject.name} stopped movement");
            }
        }
        
        /// <summary>
        /// Set movement speed
        /// </summary>
        public void SetMovementSpeed(float newSpeed)
        {
            moveSpeed = Mathf.Max(0.1f, newSpeed);
            if (navAgent != null)
            {
                navAgent.speed = moveSpeed;
            }
        }
        
        /// <summary>
        /// Set formation offset for this unit
        /// </summary>
        public void SetFormationOffset(Vector3 offset)
        {
            formationOffset = offset;
            maintainFormation = offset != Vector3.zero;
        }
        
        /// <summary>
        /// Enable or disable formation maintenance
        /// </summary>
        public void SetFormationMaintenance(bool enabled)
        {
            maintainFormation = enabled;
        }
        
        /// <summary>
        /// Check if a position is reachable
        /// </summary>
        public bool IsPositionReachable(Vector3 position)
        {
            if (navAgent == null) return false;
            
            NavMeshPath path = new NavMeshPath();
            return navAgent.CalculatePath(position, path) && path.status == NavMeshPathStatus.PathComplete;
        }
        
        /// <summary>
        /// Get the current path corners
        /// </summary>
        public Vector3[] GetCurrentPath()
        {
            if (navAgent == null || !navAgent.hasPath) return new Vector3[0];
            
            return navAgent.path.corners;
        }
        
        private void OnDrawGizmosSelected()
        {
            // Draw destination
            if (hasDestination)
            {
                Gizmos.color = Color.green;
                Gizmos.DrawWireSphere(destination, 0.5f);
                Gizmos.DrawLine(transform.position, destination);
            }
            
            // Draw formation offset
            if (maintainFormation && formationOffset != Vector3.zero)
            {
                Gizmos.color = Color.blue;
                Vector3 formationPos = transform.position + formationOffset;
                Gizmos.DrawWireCube(formationPos, Vector3.one * 0.5f);
            }
            
            // Draw current path
            if (navAgent != null && navAgent.hasPath)
            {
                Gizmos.color = Color.yellow;
                Vector3[] corners = navAgent.path.corners;
                for (int i = 0; i < corners.Length - 1; i++)
                {
                    Gizmos.DrawLine(corners[i], corners[i + 1]);
                }
            }
        }
    }
}
