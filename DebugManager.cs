using System.Collections.Generic;
using UnityEngine;

namespace ColdVor.RTS
{
    /// <summary>
    /// Centralized debug manager for clean, non-spammy debug output
    /// </summary>
    public class DebugManager : SingletonBase<DebugManager>
    {
        [Header("Debug Categories")]
        [SerializeField] private bool enableCombatDebug = true;
        [SerializeField] private bool enableAIDebug = true;
        [SerializeField] private bool enableDetectionDebug = false;
        [SerializeField] private bool enableMovementDebug = false;
        [SerializeField] private bool enableSystemDebug = true;

        [Header("Debug Timing")]
        [SerializeField] private float debugInterval = 3f; // Seconds between debug outputs
        [SerializeField] private int maxDebugPerFrame = 2; // Max debug messages per frame

        [Header("Debug Output")]
        [SerializeField] private bool showStackTrace = false; // Show stack traces in debug
        [SerializeField] private bool useCleanOutput = true; // Use clean, simplified output

        // Timing control
        private Dictionary<string, float> lastDebugTime = new Dictionary<string, float>();
        private int debugCountThisFrame = 0;

        protected override void OnSingletonAwake()
        {
            // Reset frame counter each frame
            InvokeRepeating(nameof(ResetFrameCounter), 0f, Time.fixedDeltaTime);

            // Configure Unity's stack trace logging if we want clean output
            if (useCleanOutput && !showStackTrace)
            {
                // Disable stack traces for regular log messages
                Application.SetStackTraceLogType(LogType.Log, StackTraceLogType.None);
            }
        }

        private void ResetFrameCounter()
        {
            debugCountThisFrame = 0;
        }

        /// <summary>
        /// Check if debug should be output for this category and key
        /// </summary>
        private bool ShouldDebug(string category, string key)
        {
            if (debugCountThisFrame >= maxDebugPerFrame) return false;

            string fullKey = $"{category}_{key}";
            
            if (lastDebugTime.ContainsKey(fullKey))
            {
                if (Time.time - lastDebugTime[fullKey] < debugInterval)
                    return false;
            }

            lastDebugTime[fullKey] = Time.time;
            debugCountThisFrame++;
            return true;
        }

        /// <summary>
        /// Output a clean debug message without stack trace
        /// </summary>
        private void LogClean(string message)
        {
            if (useCleanOutput)
            {
                // Use Unity's console directly for clean output
                UnityEngine.Debug.unityLogger.Log(LogType.Log, message);
            }
            else
            {
                Debug.Log(message);
            }
        }

        #region Combat Debug
        public void LogCombat(string message, string key = "general")
        {
            if (enableCombatDebug && ShouldDebug("combat", key))
            {
                LogClean($"[Combat] {message}");
            }
        }

        public void LogDamage(Unit attacker, Unit target, int damage, int newHealth)
        {
            if (enableCombatDebug && ShouldDebug("combat", "damage"))
            {
                LogClean($"[Combat] {attacker.name} → {target.name}: {damage} damage (Health: {newHealth})");
            }
        }

        public void LogUnitDeath(Unit unit)
        {
            if (enableCombatDebug)
            {
                LogClean($"[Combat] {unit.name} destroyed");
            }
        }
        #endregion

        #region AI Debug
        public void LogAIStateChange(AI ai, AIState oldState, AIState newState)
        {
            if (enableAIDebug && ShouldDebug("ai", ai.name))
            {
                LogClean($"[AI] {ai.name}: {oldState} → {newState}");
            }
        }

        public void LogAITargeting(AI ai, Unit target, string action)
        {
            if (enableAIDebug && ShouldDebug("ai", $"{ai.name}_targeting"))
            {
                LogClean($"[AI] {ai.name}: {action} {(target ? target.name : "null")}");
            }
        }

        public void LogAISuppression(AI ai, float suppressionLevel, float moraleLevel)
        {
            if (enableAIDebug && ShouldDebug("ai", $"{ai.name}_suppression"))
            {
                LogClean($"[AI] {ai.name}: Suppression {suppressionLevel:F0}%, Morale {moraleLevel:F0}%");
            }
        }

        public void LogAI(string message)
        {
            if (enableAIDebug)
            {
                LogClean($"[AI] {message}");
            }
        }
        #endregion

        #region Detection Debug
        public void LogDetection(Unit unit, Unit target, string action)
        {
            if (enableDetectionDebug && ShouldDebug("detection", unit.name))
            {
                LogClean($"[Detection] {unit.name}: {action} {target.name}");
            }
        }

        public void LogDetectionScan(Unit unit, int enemiesFound, int friendliesFound)
        {
            if (enableDetectionDebug && ShouldDebug("detection", $"{unit.name}_scan"))
            {
                LogClean($"[Detection] {unit.name}: Found {enemiesFound} enemies, {friendliesFound} friendlies");
            }
        }
        #endregion

        #region Movement Debug
        public void LogMovement(Unit unit, Vector3 destination, string reason)
        {
            if (enableMovementDebug && ShouldDebug("movement", unit.name))
            {
                LogClean($"[Movement] {unit.name}: Moving to {destination} ({reason})");
            }
        }

        public void LogMovementComplete(Unit unit, string reason)
        {
            if (enableMovementDebug && ShouldDebug("movement", $"{unit.name}_complete"))
            {
                LogClean($"[Movement] {unit.name}: Reached destination ({reason})");
            }
        }
        #endregion

        #region System Debug
        public void LogSystem(string system, string message)
        {
            if (enableSystemDebug && ShouldDebug("system", system))
            {
                LogClean($"[{system}] {message}");
            }
        }

        public void LogError(string system, string error)
        {
            // Errors always show stack trace for debugging
            Debug.LogError($"[{system}] ERROR: {error}");
        }

        public void LogWarning(string system, string warning)
        {
            // Warnings always show stack trace for debugging
            Debug.LogWarning($"[{system}] WARNING: {warning}");
        }
        #endregion



        #region Performance Debug
        public void LogPerformance(string operation, float timeMs)
        {
            if (timeMs > 5f && ShouldDebug("performance", operation))
            {
                // Performance warnings show stack trace for debugging
                Debug.LogWarning($"[Performance] {operation}: {timeMs:F2}ms (slow)");
            }
        }
        #endregion

        #region Debug Controls
        [ContextMenu("Enable All Debug")]
        public void EnableAllDebug()
        {
            enableCombatDebug = true;
            enableAIDebug = true;
            enableDetectionDebug = true;
            enableMovementDebug = true;
            enableSystemDebug = true;
        }

        [ContextMenu("Disable All Debug")]
        public void DisableAllDebug()
        {
            enableCombatDebug = false;
            enableAIDebug = false;
            enableDetectionDebug = false;
            enableMovementDebug = false;
            enableSystemDebug = false;
        }

        [ContextMenu("Combat Debug Only")]
        public void CombatDebugOnly()
        {
            DisableAllDebug();
            enableCombatDebug = true;
        }

        [ContextMenu("AI Debug Only")]
        public void AIDebugOnly()
        {
            DisableAllDebug();
            enableAIDebug = true;
        }

        [ContextMenu("Essential Debug Only")]
        public void EssentialDebugOnly()
        {
            DisableAllDebug();
            enableCombatDebug = true;
            enableAIDebug = true;
            enableSystemDebug = true;
        }

        [ContextMenu("Toggle Clean Output")]
        public void ToggleCleanOutput()
        {
            useCleanOutput = !useCleanOutput;
            LogSystem("DebugManager", $"Clean output: {(useCleanOutput ? "Enabled" : "Disabled")}");

            // Update stack trace settings
            if (useCleanOutput && !showStackTrace)
            {
                Application.SetStackTraceLogType(LogType.Log, StackTraceLogType.None);
            }
            else
            {
                Application.SetStackTraceLogType(LogType.Log, StackTraceLogType.ScriptOnly);
            }
        }

        [ContextMenu("Show Debug Status")]
        public void ShowDebugStatus()
        {
            LogClean("=== DEBUG MANAGER STATUS ===");
            LogClean($"Combat Debug: {enableCombatDebug}");
            LogClean($"AI Debug: {enableAIDebug}");
            LogClean($"Detection Debug: {enableDetectionDebug}");
            LogClean($"Movement Debug: {enableMovementDebug}");
            LogClean($"System Debug: {enableSystemDebug}");
            LogClean($"Clean Output: {useCleanOutput}");
            LogClean($"Show Stack Trace: {showStackTrace}");
            LogClean($"Debug Interval: {debugInterval}s");
            LogClean($"Max Debug Per Frame: {maxDebugPerFrame}");
            LogClean("============================");
        }

        /// <summary>
        /// Show useful game state information
        /// </summary>
        [ContextMenu("Show Game State")]
        public void ShowGameState()
        {
            LogClean("=== GAME STATE SUMMARY ===");

            // Count units by faction
            Unit[] allUnits = FindObjectsByType<Unit>(FindObjectsSortMode.None);
            int playerUnits = 0, enemyUnits = 0, neutralUnits = 0, allyUnits = 0;

            foreach (Unit unit in allUnits)
            {
                switch (unit.Faction)
                {
                    case Faction.Player: playerUnits++; break;
                    case Faction.Enemy: enemyUnits++; break;
                    case Faction.Neutral: neutralUnits++; break;
                    case Faction.Ally: allyUnits++; break;
                }
            }

            LogClean($"Units - Player: {playerUnits}, Enemy: {enemyUnits}, Neutral: {neutralUnits}, Ally: {allyUnits}");

            // Show active projectiles
            if (CombatSystem.Instance != null)
            {
                LogClean($"Active Projectiles: {CombatSystem.Instance.GetActiveProjectileCount()}");
            }

            // Show frame rate
            LogClean($"FPS: {(1f / Time.deltaTime):F1}");
            LogClean("==========================");
        }
        #endregion
    }
}
