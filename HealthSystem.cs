using UnityEngine;
using System;

namespace ColdVor.RTS
{
    /// <summary>
    /// Dedicated health system component for unit health management.
    /// Handles all health-related functionality separate from other systems.
    /// </summary>
    public class HealthSystem : MonoBehaviour, IHealthProvider
    {
        [Header("Health Configuration")]
        [SerializeField] private int maxHealth = 100;
        [SerializeField] private int currentHealth;
        [SerializeField] private bool canRegenerate = false;
        [SerializeField] private float regenerationRate = 1f; // HP per second
        [SerializeField] private float regenerationDelay = 5f; // Delay after taking damage
        
        [Header("Damage Settings")]
        [SerializeField] private bool invulnerable = false;
        [SerializeField] private float damageReduction = 0f; // 0-1 percentage
        [SerializeField] private bool showDamageNumbers = true;
        
        [Header("Death Settings")]
        [SerializeField] private bool destroyOnDeath = true;
        [SerializeField] private float deathDelay = 2f;
        [SerializeField] private GameObject deathEffect;
        
        // Health state
        private bool isDead = false;
        private float lastDamageTime;
        private float lastRegenerationTime;
        
        // Cached components
        private Unit ownerUnit;
        
        // Events
        public event Action<HealthSystem, int, int> OnHealthChanged; // oldHealth, newHealth
        public event Action<HealthSystem, int, Unit> OnDamageTaken; // damage, attacker
        public event Action<HealthSystem, int> OnHealed; // amount
        public event Action<HealthSystem> OnDeath;
        public event Action<HealthSystem> OnRevived;
        public event Action<HealthSystem> OnHealthCritical; // Below 25%
        
        // Properties - IHealthProvider implementation
        public int CurrentHealth => currentHealth;
        public int MaxHealth => maxHealth;
        public bool IsAlive => !isDead && currentHealth > 0;
        public bool IsDead => isDead;
        public float HealthPercentage => maxHealth > 0 ? (float)currentHealth / maxHealth : 0f;
        
        // Additional properties
        public bool IsHealthCritical => HealthPercentage <= 0.25f && IsAlive;
        public bool IsHealthLow => HealthPercentage <= 0.5f && IsAlive;
        public bool IsFullHealth => currentHealth >= maxHealth;
        public bool CanRegenerate => canRegenerate && IsAlive && Time.time - lastDamageTime >= regenerationDelay;
        
        private void Awake()
        {
            ownerUnit = GetComponent<Unit>();

            // Sync with Unit's health settings if available
            if (ownerUnit != null)
            {
                maxHealth = ownerUnit.MaxHealth;
            }

            // Initialize health
            if (currentHealth <= 0)
            {
                currentHealth = maxHealth;
            }

            lastRegenerationTime = Time.time;
        }
        
        private void Update()
        {
            UpdateRegeneration();
        }
        
        /// <summary>
        /// Update health regeneration if enabled
        /// </summary>
        private void UpdateRegeneration()
        {
            if (!CanRegenerate || IsFullHealth) return;
            
            if (Time.time - lastRegenerationTime >= 1f) // Regenerate every second
            {
                int regenAmount = Mathf.RoundToInt(regenerationRate);
                Heal(regenAmount);
                lastRegenerationTime = Time.time;
            }
        }
        
        /// <summary>
        /// Take damage from an attacker
        /// </summary>
        public void TakeDamage(int damage, Unit attacker = null)
        {
            if (isDead || invulnerable || damage <= 0) return;
            
            // Apply damage reduction
            int actualDamage = Mathf.RoundToInt(damage * (1f - damageReduction));
            actualDamage = Mathf.Max(1, actualDamage); // Minimum 1 damage
            
            int oldHealth = currentHealth;
            currentHealth = Mathf.Max(0, currentHealth - actualDamage);
            lastDamageTime = Time.time;
            
            // Trigger events
            OnDamageTaken?.Invoke(this, actualDamage, attacker);
            HealthSystemEvents.TriggerDamageTaken(this, actualDamage, attacker);
            
            OnHealthChanged?.Invoke(this, oldHealth, currentHealth);
            HealthSystemEvents.TriggerHealthChanged(this, oldHealth, currentHealth);
            
            // Check for critical health
            if (IsHealthCritical)
            {
                OnHealthCritical?.Invoke(this);
            }
            
            // Check for death
            if (currentHealth <= 0 && !isDead)
            {
                Die();
            }
            
            // Show damage numbers
            if (showDamageNumbers)
            {
                ShowDamageNumber(actualDamage);
            }
            
            if (DebugManager.Instance != null)
            {
                DebugManager.Instance.LogCombat($"{gameObject.name} took {actualDamage} damage from {(attacker?.name ?? "unknown")}. Health: {currentHealth}/{maxHealth}");
            }
        }
        
        /// <summary>
        /// Heal the unit
        /// </summary>
        public void Heal(int amount)
        {
            if (isDead || amount <= 0) return;
            
            int oldHealth = currentHealth;
            currentHealth = Mathf.Min(maxHealth, currentHealth + amount);
            
            if (currentHealth != oldHealth)
            {
                OnHealed?.Invoke(this, amount);
                HealthSystemEvents.TriggerHealed(this, amount);
                
                OnHealthChanged?.Invoke(this, oldHealth, currentHealth);
                HealthSystemEvents.TriggerHealthChanged(this, oldHealth, currentHealth);
                
                if (DebugManager.Instance != null)
                {
                    DebugManager.Instance.LogSystem("HealthSystem", $"{gameObject.name} healed {amount}. Health: {currentHealth}/{maxHealth}");
                }
            }
        }
        
        /// <summary>
        /// Handle unit death
        /// </summary>
        private void Die()
        {
            if (isDead) return;
            
            isDead = true;
            currentHealth = 0;
            
            // Trigger death events
            OnDeath?.Invoke(this);
            HealthSystemEvents.TriggerDeath(this);
            
            // Spawn death effect
            if (deathEffect != null)
            {
                Instantiate(deathEffect, transform.position, transform.rotation);
            }
            
            // Handle destruction
            if (destroyOnDeath)
            {
                Invoke(nameof(DestroyUnit), deathDelay);
            }
            
            if (DebugManager.Instance != null)
            {
                DebugManager.Instance.LogCombat($"{gameObject.name} has died");
            }
        }
        
        /// <summary>
        /// Revive the unit (for special abilities or respawn systems)
        /// </summary>
        public void Revive(int healthAmount = -1)
        {
            if (!isDead) return;
            
            isDead = false;
            currentHealth = healthAmount > 0 ? Mathf.Min(healthAmount, maxHealth) : maxHealth;
            
            // Cancel destruction if pending
            CancelInvoke(nameof(DestroyUnit));
            
            OnRevived?.Invoke(this);
            HealthSystemEvents.TriggerRevived(this);
            
            OnHealthChanged?.Invoke(this, 0, currentHealth);
            HealthSystemEvents.TriggerHealthChanged(this, 0, currentHealth);
            
            if (DebugManager.Instance != null)
            {
                DebugManager.Instance.LogSystem("HealthSystem", $"{gameObject.name} has been revived with {currentHealth} health");
            }
        }
        
        /// <summary>
        /// Set maximum health (and optionally current health)
        /// </summary>
        public void SetMaxHealth(int newMaxHealth, bool adjustCurrentHealth = true)
        {
            int oldMaxHealth = maxHealth;
            maxHealth = Mathf.Max(1, newMaxHealth);
            
            if (adjustCurrentHealth)
            {
                // Scale current health proportionally
                float healthRatio = oldMaxHealth > 0 ? (float)currentHealth / oldMaxHealth : 1f;
                int oldHealth = currentHealth;
                currentHealth = Mathf.RoundToInt(maxHealth * healthRatio);
                
                OnHealthChanged?.Invoke(this, oldHealth, currentHealth);
                HealthSystemEvents.TriggerHealthChanged(this, oldHealth, currentHealth);
            }
        }
        
        /// <summary>
        /// Set invulnerability state
        /// </summary>
        public void SetInvulnerable(bool invulnerable)
        {
            this.invulnerable = invulnerable;
        }
        
        /// <summary>
        /// Set damage reduction percentage (0-1)
        /// </summary>
        public void SetDamageReduction(float reduction)
        {
            damageReduction = Mathf.Clamp01(reduction);
        }
        
        /// <summary>
        /// Enable or disable regeneration
        /// </summary>
        public void SetRegeneration(bool enabled, float rate = 1f, float delay = 5f)
        {
            canRegenerate = enabled;
            regenerationRate = rate;
            regenerationDelay = delay;
        }
        
        /// <summary>
        /// Show damage number visual effect
        /// </summary>
        private void ShowDamageNumber(int damage)
        {
            // This would integrate with a damage number UI system
            // For now, just log it
            if (DebugManager.Instance != null)
            {
                DebugManager.Instance.LogCombat($"Damage: -{damage}");
            }
        }
        
        /// <summary>
        /// Destroy the unit GameObject
        /// </summary>
        private void DestroyUnit()
        {
            if (gameObject != null)
            {
                Destroy(gameObject);
            }
        }
        
        /// <summary>
        /// Get health status as a string
        /// </summary>
        public string GetHealthStatus()
        {
            if (isDead) return "Dead";
            if (IsHealthCritical) return "Critical";
            if (IsHealthLow) return "Wounded";
            if (IsFullHealth) return "Healthy";
            return "Injured";
        }
        
        /// <summary>
        /// Get health bar color based on current health
        /// </summary>
        public Color GetHealthBarColor()
        {
            if (isDead) return Color.black;
            if (IsHealthCritical) return Color.red;
            if (IsHealthLow) return Color.yellow;
            return Color.green;
        }
        
        private void OnValidate()
        {
            // Ensure valid values in editor
            maxHealth = Mathf.Max(1, maxHealth);
            currentHealth = Mathf.Clamp(currentHealth, 0, maxHealth);
            regenerationRate = Mathf.Max(0f, regenerationRate);
            regenerationDelay = Mathf.Max(0f, regenerationDelay);
            damageReduction = Mathf.Clamp01(damageReduction);
            deathDelay = Mathf.Max(0f, deathDelay);
        }
    }
}
