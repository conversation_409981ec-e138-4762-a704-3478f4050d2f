using System.Collections.Generic;
using UnityEngine;

namespace ColdVor.RTS
{
    /// <summary>
    /// Manages combat mechanics, weapon systems, and projectiles
    /// Handles damage calculation, ballistics, and combat effects
    /// </summary>
    public class CombatSystem : SingletonBase<CombatSystem>
    {
        [Header("Projectile Settings")]
        [SerializeField] private GameObject defaultProjectilePrefab;
        [SerializeField] private LayerMask projectileCollisionMask = (1 << 3); // Only hit units on layer 3

        [Header("Combat Effects")]
        [SerializeField] private GameObject muzzleFlashPrefab;
        [SerializeField] private GameObject hitEffectPrefab;
        [SerializeField] private GameObject explosionEffectPrefab;

        [Header("Audio")]
        [SerializeField] private AudioClip[] weaponFireSounds;
        [SerializeField] private AudioClip[] impactSounds;
        [SerializeField] private AudioSource audioSource;

        // Active projectiles tracking
        private List<Projectile> activeProjectiles = new List<Projectile>();

        // Robust projectile pool manager
        private ProjectilePoolManager poolManager;

        // Events
        public System.Action<Unit, Unit, int> OnDamageDealt;
        public System.Action<Unit> OnUnitKilled;
        public System.Action<Vector3, float> OnExplosion;

        protected override void OnSingletonAwake()
        {
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }

            // Initialize projectile pool manager
            poolManager = GetComponent<ProjectilePoolManager>();
            if (poolManager == null)
            {
                poolManager = gameObject.AddComponent<ProjectilePoolManager>();
            }

            // Create a default projectile prefab if none is assigned
            if (defaultProjectilePrefab == null)
            {
                CreateDefaultProjectilePrefab();
            }
        }

        /// <summary>
        /// Create a simple default projectile prefab at runtime
        /// </summary>
        private void CreateDefaultProjectilePrefab()
        {
            GameObject projectile = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            projectile.name = "DefaultProjectile";
            projectile.transform.localScale = Vector3.one * 0.1f;

            // Add Rigidbody
            Rigidbody rb = projectile.AddComponent<Rigidbody>();
            rb.useGravity = false;
            rb.linearDamping = 0f;

            // Add Projectile component
            projectile.AddComponent<Projectile>();

            // Create proper glowing material for visibility
            Renderer renderer = projectile.GetComponent<Renderer>();
            if (renderer != null)
            {
                Material mat = new Material(Shader.Find("Standard"));
                mat.color = Color.yellow;
                // Make it glow
                mat.EnableKeyword("_EMISSION");
                mat.SetColor("_EmissionColor", Color.yellow * 1.5f);
                mat.SetFloat("_Metallic", 0.1f);
                mat.SetFloat("_Smoothness", 0.8f);
                renderer.material = mat;
            }

            // Add a light component for proper lighting
            Light projectileLight = projectile.AddComponent<Light>();
            projectileLight.type = LightType.Point;
            projectileLight.color = Color.yellow;
            projectileLight.intensity = 2f;
            projectileLight.range = 5f;
            projectileLight.shadows = LightShadows.None; // Performance

            // Add a trail renderer for better visuals
            TrailRenderer trail = projectile.AddComponent<TrailRenderer>();
            trail.material = new Material(Shader.Find("Sprites/Default"));
            trail.material.color = Color.yellow;
            trail.startWidth = 0.05f;
            trail.endWidth = 0.01f;
            trail.time = 0.2f;
            trail.minVertexDistance = 0.1f;

            // Store as prefab
            defaultProjectilePrefab = projectile;

            // Deactivate the template
            projectile.SetActive(false);

            if (DebugManager.Instance != null)
            {
                DebugManager.Instance.LogSystem("CombatSystem", "Created enhanced default projectile prefab with lighting");
            }
        }

        private void Update()
        {
            UpdateProjectiles();
        }

        /// <summary>
        /// Fire a projectile from attacker to target using new parameter system
        /// </summary>
        public void FireProjectileWithParameters(Unit attacker, Unit target, Vector3 targetPosition, ProjectileParameters parameters, GameObject projectilePrefab)
        {
            if (attacker == null || target == null)
            {
                if (DebugManager.Instance != null)
                {
                    DebugManager.Instance.LogWarning("CombatSystem", "FireProjectileWithParameters: Missing required parameters");
                }
                return;
            }

            Vector3 firePosition = GetFirePosition(attacker);

            // Apply suppression to target from incoming fire
            ApplyIncomingFireSuppression(target);

            // Determine which prefab to use
            GameObject prefabToUse = projectilePrefab ?? defaultProjectilePrefab;
            if (prefabToUse == null)
            {
                if (defaultProjectilePrefab == null)
                {
                    Debug.LogWarning("CombatSystem.FireProjectileWithParameters: No projectile prefab available, creating default...");
                    CreateDefaultProjectilePrefab();
                }
                prefabToUse = defaultProjectilePrefab;
            }

            // Create projectile using robust pool manager
            GameObject projectileObj = poolManager.GetPooledProjectile(prefabToUse, firePosition, Quaternion.LookRotation((targetPosition - firePosition).normalized));

            if (projectileObj == null)
            {
                Debug.LogError($"CombatSystem.FireProjectileWithParameters: Failed to create projectile for {attacker.name}");
                return;
            }

            Projectile projectile = projectileObj.GetComponent<Projectile>();
            if (projectile == null)
            {
                projectile = projectileObj.AddComponent<Projectile>();
            }

            // Initialize projectile with parameters
            projectile.Initialize(attacker, target, targetPosition, parameters);

            // Activate the projectile
            projectileObj.SetActive(true);
            activeProjectiles.Add(projectile);

            // Configure projectile collision
            ConfigureProjectileCollision(projectileObj, attacker);

            // Play effects
            Vector3 direction = (targetPosition - firePosition).normalized;
            PlayMuzzleFlash(firePosition, direction);
            PlayWeaponSound(parameters.weaponType);
        }

        /// <summary>
        /// Fire an instant hit weapon using new parameter system
        /// </summary>
        public void FireInstantHitWithParameters(Unit attacker, Unit target, Vector3 targetPosition, ProjectileParameters parameters)
        {
            if (attacker == null || target == null) return;

            Vector3 firePosition = GetFirePosition(attacker);
            Vector3 direction = (targetPosition - firePosition).normalized;

            // Perform raycast
            if (Physics.Raycast(firePosition, direction, out RaycastHit hit, parameters.accuracy, projectileCollisionMask))
            {
                Unit hitUnit = hit.collider.GetComponent<Unit>();
                if (hitUnit != null)
                {
                    ApplyDamage(attacker, hitUnit, parameters.damage, hit.point);
                }
                else
                {
                    // Hit terrain or obstacle
                    PlayHitEffect(hit.point, hit.normal);
                }
            }

            // Play effects
            PlayMuzzleFlash(firePosition, direction);
            PlayWeaponSound(parameters.weaponType);
        }





        /// <summary>
        /// Calculate ballistic trajectory target for artillery
        /// </summary>
        private Vector3 CalculateBallisticTarget(Vector3 firePosition, Vector3 targetPosition, float projectileSpeed)
        {
            // Simple ballistic calculation - aim higher for longer range
            float distance = Vector3.Distance(firePosition, targetPosition);
            float timeToTarget = distance / projectileSpeed;
            float gravity = Physics.gravity.magnitude;

            // Calculate required launch angle for ballistic trajectory
            float heightOffset = 0.5f * gravity * timeToTarget * timeToTarget;
            return targetPosition + Vector3.up * heightOffset;
        }

        /// <summary>
        /// Calculate lead target for anti-air weapons
        /// </summary>
        private Vector3 CalculateLeadTarget(Unit target, Vector3 firePosition, float projectileSpeed)
        {
            if (target == null) return firePosition;

            // Get target velocity
            Rigidbody targetRb = target.GetComponent<Rigidbody>();
            Vector3 targetVelocity = targetRb != null ? targetRb.linearVelocity : Vector3.zero;

            // Calculate interception point
            Vector3 toTarget = target.transform.position - firePosition;
            float distance = toTarget.magnitude;
            float timeToTarget = distance / projectileSpeed;

            // Lead the target based on its velocity
            Vector3 leadPosition = target.transform.position + targetVelocity * timeToTarget;
            return leadPosition;
        }

        /// <summary>
        /// Get guided missile target position with prediction
        /// </summary>
        private Vector3 GetGuidedTargetPosition(Unit target, Vector3 firePosition, float projectileSpeed)
        {
            // Guided missiles use advanced prediction
            return CalculateLeadTarget(target, firePosition, projectileSpeed);
        }

        /// <summary>
        /// Create energy beam visual effect
        /// </summary>
        private void CreateEnergyBeamEffect(Vector3 startPos, Vector3 endPos)
        {
            // Create a line renderer for the energy beam
            GameObject beamObj = new GameObject("EnergyBeam");
            LineRenderer beam = beamObj.AddComponent<LineRenderer>();

            Material beamMaterial = new Material(Shader.Find("Sprites/Default"));
            beamMaterial.color = Color.cyan;
            beam.material = beamMaterial;
            beam.startWidth = 0.1f;
            beam.endWidth = 0.05f;
            beam.positionCount = 2;
            beam.useWorldSpace = true;

            beam.SetPosition(0, startPos);
            beam.SetPosition(1, endPos);

            // Destroy beam after short duration
            Destroy(beamObj, 0.1f);
        }



        /// <summary>
        /// Apply damage to a target unit
        /// </summary>
        public void ApplyDamage(Unit attacker, Unit target, int damage, Vector3 hitPoint)
        {
            if (target == null || target.IsDead) return; // Don't damage dead units

            // Check if damage should be applied based on faction relationships
            if (attacker != null && FactionManager.Instance != null)
            {
                bool shouldApply = FactionManager.Instance.ShouldApplyDamage(attacker.Faction, target.Faction);

                if (!shouldApply)
                {
                    return;
                }
            }

            int finalDamage = CalculateDamage(attacker, target, damage);

            // Clean combat logging
            if (DebugManager.Instance != null)
            {
                DebugManager.Instance.LogCombat($"{attacker.name} → {target.name}: {finalDamage} damage");
            }

            // Apply damage
            target.TakeDamage(finalDamage, attacker);

            // Play hit effect
            PlayHitEffect(hitPoint, Vector3.up);

            // Trigger events
            OnDamageDealt?.Invoke(attacker, target, finalDamage);

            // Report combat to intelligence network
            if (attacker != null && IntelligenceNetwork.Instance != null)
            {
                IntelligenceNetwork.Instance.ReportCombatEvent(attacker, target, finalDamage);
            }

            if (target.CurrentHealth <= 0)
            {
                OnUnitKilled?.Invoke(target);

                // Report unit death to intelligence network
                if (IntelligenceNetwork.Instance != null)
                {
                    IntelligenceNetwork.Instance.ReportUnitDestroyed(target, attacker);
                }
            }
        }

        /// <summary>
        /// Create an explosion at the specified position
        /// </summary>
        public void CreateExplosion(Vector3 position, float radius, int damage, Unit attacker = null)
        {
            // Find all units in explosion radius
            Collider[] hitColliders = Physics.OverlapSphere(position, radius);
            
            foreach (Collider col in hitColliders)
            {
                Unit unit = col.GetComponent<Unit>();
                if (unit != null)
                {
                    // Calculate damage based on distance
                    float distance = Vector3.Distance(position, unit.transform.position);
                    int explosionDamage = RTSUtilities.CalculateDistanceBasedDamage(damage, distance, radius);
                    
                    if (explosionDamage > 0)
                    {
                        ApplyDamage(attacker, unit, explosionDamage, unit.transform.position);
                    }
                }
            }

            // Apply area suppression from explosion
            ApplyExplosionSuppression(position, radius);

            // Play explosion effect
            PlayExplosionEffect(position);
            OnExplosion?.Invoke(position, radius);
        }

        private void UpdateProjectiles()
        {
            for (int i = activeProjectiles.Count - 1; i >= 0; i--)
            {
                if (activeProjectiles[i] == null || !activeProjectiles[i].gameObject.activeInHierarchy)
                {
                    activeProjectiles.RemoveAt(i);
                }
            }
        }

        private GameObject CreateProjectile(GameObject prefab, Vector3 position)
        {
            if (prefab == null)
            {
                // Create a reasonably sized visible projectile
                GameObject projectile = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                projectile.name = "DefaultProjectile";
                projectile.transform.localScale = Vector3.one * 0.2f; // Smaller, more reasonable size
                projectile.transform.position = position;

                // Remove the collider from the primitive to avoid conflicts
                Collider primitiveCollider = projectile.GetComponent<Collider>();
                if (primitiveCollider != null)
                {
                    DestroyImmediate(primitiveCollider);
                }

                // Add a Rigidbody for physics
                Rigidbody rb = projectile.GetComponent<Rigidbody>();
                if (rb == null)
                {
                    rb = projectile.AddComponent<Rigidbody>();
                }
                rb.useGravity = false; // We'll handle gravity in the Projectile script
                rb.linearDamping = 0f;
                rb.angularDamping = 0f;
                rb.mass = 0.1f;
                rb.interpolation = RigidbodyInterpolation.Interpolate; // Smooth movement
                rb.mass = 0.1f;

                // Add a trigger collider for collision detection
                SphereCollider triggerCollider = projectile.AddComponent<SphereCollider>();
                triggerCollider.isTrigger = true;
                triggerCollider.radius = 0.2f; // Smaller collision radius to avoid false hits

                // Create a bright, visible material
                Renderer renderer = projectile.GetComponent<Renderer>();
                if (renderer != null)
                {
                    Material projectileMat = new Material(Shader.Find("Unlit/Color"));
                    projectileMat.color = Color.yellow; // Bright yellow for visibility
                    renderer.material = projectileMat;

                    // Make sure it's not affected by lighting
                    renderer.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.Off;
                    renderer.receiveShadows = false;
                }

                // Add a trail renderer for better visibility
                TrailRenderer trail = projectile.AddComponent<TrailRenderer>();
                trail.time = 0.5f; // Shorter trail
                trail.startWidth = 0.1f;
                trail.endWidth = 0.02f;
                Material trailMat = new Material(Shader.Find("Unlit/Color"));
                trailMat.color = Color.yellow;
                trail.material = trailMat;
                trail.startColor = Color.yellow;
                trail.endColor = new Color(1f, 1f, 0f, 0f);

                // Add a small light component for visibility
                Light projectileLight = projectile.AddComponent<Light>();
                projectileLight.type = LightType.Point;
                projectileLight.color = Color.yellow;
                projectileLight.intensity = 1f;
                projectileLight.range = 5f;


                return projectile;
            }
            else
            {
                GameObject projectile = Instantiate(prefab, position, Quaternion.identity);
                Debug.Log($"CombatSystem: Created prefab projectile {prefab.name} at {position}");
                return projectile;
            }
        }

        private Vector3 GetFirePosition(Unit unit)
        {
            // Try to find a weapon mount point, otherwise use unit center + height offset
            Transform weaponMount = unit.transform.Find("WeaponMount");
            if (weaponMount != null)
            {
                return weaponMount.position;
            }

            // Position projectile well outside the unit's bounds to avoid immediate collision
            Vector3 basePosition = unit.transform.position + Vector3.up * 2f;
            Vector3 forward = unit.transform.forward;
            return basePosition + forward * 4f; // Spawn 4 units in front of the unit
        }

        private Vector3 GetTargetPosition(Unit target)
        {
            // Aim for center mass
            Bounds bounds = target.GetSelectionBounds();
            return bounds.center;
        }

        private int CalculateDamage(Unit attacker, Unit target, int baseDamage)
        {
            // Basic damage calculation - can be enhanced with armor, unit type modifiers, etc.
            float damageMultiplier = 1f;
            
            // Unit type effectiveness (example: tanks take less damage from small arms)
            if (attacker != null)
            {
                damageMultiplier *= GetUnitTypeEffectiveness(attacker.UnitType, target.UnitType);
            }
            
            return Mathf.RoundToInt(baseDamage * damageMultiplier);
        }

        private float GetUnitTypeEffectiveness(UnitType attackerType, UnitType targetType)
        {
            // Comprehensive effectiveness matrix based on realistic combat dynamics
            switch (attackerType)
            {
                case UnitType.Infantry:
                    switch (targetType)
                    {
                        case UnitType.Infantry: return 1.0f;      // Equal effectiveness
                        case UnitType.LightVehicle: return 0.8f; // Reduced vs armor
                        case UnitType.HeavyVehicle: return 0.4f; // Poor vs heavy armor
                        case UnitType.Tank: return 0.3f;         // Very poor vs tanks
                        case UnitType.Aircraft: return 0.2f;     // Very poor vs aircraft
                        case UnitType.Building: return 1.2f;     // Good vs buildings
                        default: return 1.0f;
                    }

                case UnitType.LightVehicle:
                    switch (targetType)
                    {
                        case UnitType.Infantry: return 1.5f;     // Excellent vs infantry
                        case UnitType.LightVehicle: return 1.0f; // Equal effectiveness
                        case UnitType.HeavyVehicle: return 0.7f; // Reduced vs heavy armor
                        case UnitType.Tank: return 0.5f;         // Poor vs tanks
                        case UnitType.Aircraft: return 0.3f;     // Poor vs aircraft
                        case UnitType.Building: return 0.8f;     // Reduced vs buildings
                        default: return 1.0f;
                    }

                case UnitType.HeavyVehicle:
                    switch (targetType)
                    {
                        case UnitType.Infantry: return 1.8f;     // Excellent vs infantry
                        case UnitType.LightVehicle: return 1.4f; // Very good vs light vehicles
                        case UnitType.HeavyVehicle: return 1.0f; // Equal effectiveness
                        case UnitType.Tank: return 0.8f;         // Slightly reduced vs tanks
                        case UnitType.Aircraft: return 0.4f;     // Poor vs aircraft
                        case UnitType.Building: return 1.3f;     // Good vs buildings
                        default: return 1.0f;
                    }

                case UnitType.Tank:
                    switch (targetType)
                    {
                        case UnitType.Infantry: return 2.0f;     // Devastating vs infantry
                        case UnitType.LightVehicle: return 1.8f; // Excellent vs light vehicles
                        case UnitType.HeavyVehicle: return 1.3f; // Good vs heavy vehicles
                        case UnitType.Tank: return 1.0f;         // Equal effectiveness
                        case UnitType.Aircraft: return 0.3f;     // Poor vs aircraft
                        case UnitType.Building: return 1.5f;     // Excellent vs buildings
                        default: return 1.0f;
                    }

                case UnitType.Aircraft:
                    switch (targetType)
                    {
                        case UnitType.Infantry: return 1.6f;     // Very good vs infantry
                        case UnitType.LightVehicle: return 1.4f; // Good vs light vehicles
                        case UnitType.HeavyVehicle: return 1.2f; // Good vs heavy vehicles
                        case UnitType.Tank: return 1.1f;         // Slightly good vs tanks
                        case UnitType.Aircraft: return 1.0f;     // Equal effectiveness
                        case UnitType.Building: return 1.3f;     // Good vs buildings
                        default: return 1.0f;
                    }

                case UnitType.Building:
                    switch (targetType)
                    {
                        case UnitType.Infantry: return 0.8f;     // Defensive structures
                        case UnitType.LightVehicle: return 0.9f; // Slightly reduced
                        case UnitType.HeavyVehicle: return 1.0f; // Equal effectiveness
                        case UnitType.Tank: return 1.1f;         // Slightly good vs tanks
                        case UnitType.Aircraft: return 1.2f;     // Good vs aircraft (AA)
                        case UnitType.Building: return 0.5f;     // Poor vs other buildings
                        default: return 1.0f;
                    }

                default:
                    return 1.0f;
            }
        }

        private void PlayMuzzleFlash(Vector3 position, Vector3 direction)
        {
            if (muzzleFlashPrefab != null)
            {
                GameObject flash = Instantiate(muzzleFlashPrefab, position, Quaternion.LookRotation(direction));
                Destroy(flash, 0.5f);
            }
        }

        private void PlayHitEffect(Vector3 position, Vector3 normal)
        {
            if (hitEffectPrefab != null)
            {
                GameObject effect = Instantiate(hitEffectPrefab, position, Quaternion.LookRotation(normal));

                // Start immediate fade-out instead of static display
                StartCoroutine(FadeOutHitEffect(effect));
            }
        }

        private System.Collections.IEnumerator FadeOutHitEffect(GameObject effect)
        {
            if (effect == null) yield break;

            // Get all renderers and particle systems
            Renderer[] renderers = effect.GetComponentsInChildren<Renderer>();
            ParticleSystem[] particles = effect.GetComponentsInChildren<ParticleSystem>();
            Light[] lights = effect.GetComponentsInChildren<Light>();

            // Store original values
            Color[] originalColors = new Color[renderers.Length];
            float[] originalIntensities = new float[lights.Length];

            for (int i = 0; i < renderers.Length; i++)
            {
                if (renderers[i] != null && renderers[i].material != null)
                {
                    originalColors[i] = renderers[i].material.color;
                }
            }

            for (int i = 0; i < lights.Length; i++)
            {
                if (lights[i] != null)
                {
                    originalIntensities[i] = lights[i].intensity;
                }
            }

            // Smooth even fade out
            float fadeTime = 0.25f; // Perfect fade time
            float elapsed = 0f;

            while (elapsed < fadeTime && effect != null)
            {
                elapsed += Time.deltaTime;
                float progress = elapsed / fadeTime;
                float alpha = Mathf.SmoothStep(1f, 0f, progress); // Smooth even transition

                // Fade renderers
                for (int i = 0; i < renderers.Length; i++)
                {
                    if (renderers[i] != null && renderers[i].material != null)
                    {
                        Color color = originalColors[i];
                        renderers[i].material.color = new Color(color.r, color.g, color.b, alpha);
                    }
                }

                // Fade lights
                for (int i = 0; i < lights.Length; i++)
                {
                    if (lights[i] != null)
                    {
                        lights[i].intensity = originalIntensities[i] * alpha;
                    }
                }

                // Stop particle emission
                foreach (ParticleSystem ps in particles)
                {
                    if (ps != null)
                    {
                        var emission = ps.emission;
                        emission.enabled = alpha > 0.1f;
                    }
                }

                yield return null;
            }

            // Destroy the effect
            if (effect != null)
            {
                Destroy(effect);
            }
        }

        private void PlayExplosionEffect(Vector3 position)
        {
            if (explosionEffectPrefab != null)
            {
                GameObject explosion = Instantiate(explosionEffectPrefab, position, Quaternion.identity);
                Destroy(explosion, 5f);
            }
        }

        private void PlayWeaponSound(WeaponType weaponType)
        {
            if (weaponFireSounds != null && weaponFireSounds.Length > 0 && audioSource != null)
            {
                AudioClip clip = weaponFireSounds[Random.Range(0, weaponFireSounds.Length)];
                audioSource.PlayOneShot(clip);
            }
        }

        /// <summary>
        /// Configure projectile collision settings to avoid terrain hits
        /// </summary>
        private void ConfigureProjectileCollision(GameObject projectileObj, Unit attacker)
        {
            // Set projectile to a specific layer (e.g., layer 8 for projectiles)
            // This assumes you have a "Projectile" layer set up in your project
            int projectileLayer = LayerMask.NameToLayer("Projectile");
            if (projectileLayer != -1)
            {
                projectileObj.layer = projectileLayer;
            }
            else
            {
                // Fallback to a safe layer if Projectile layer doesn't exist
                projectileObj.layer = 2; // Ignore Raycast layer
            }

            // Configure physics interactions
            // Ignore collision with terrain (layer 0) and other unwanted layers
            int terrainLayer = 0; // Default layer is typically terrain
            Physics.IgnoreLayerCollision(projectileObj.layer, terrainLayer, true);
            Physics.IgnoreLayerCollision(projectileObj.layer, projectileObj.layer, true); // Projectiles don't hit each other

            // Make sure projectiles can hit units (layer 3)
            Physics.IgnoreLayerCollision(projectileObj.layer, 3, false); // Allow hitting units
        }

        /// <summary>
        /// Remove a projectile from tracking
        /// </summary>
        public void RemoveProjectile(Projectile projectile)
        {
            activeProjectiles.Remove(projectile);
        }

        /// <summary>
        /// Get the number of active projectiles (for debug purposes)
        /// </summary>
        public int GetActiveProjectileCount()
        {
            return activeProjectiles.Count;
        }
        #region Suppression System

        /// <summary>
        /// Apply suppression to target from incoming fire
        /// </summary>
        private void ApplyIncomingFireSuppression(Unit target)
        {
            if (target == null) return;

            // Apply suppression to AI units
            AI targetAI = target.GetComponent<AI>();
            if (targetAI != null)
            {
                // Reduced suppression amount for incoming fire
                float suppressionAmount = 10f;
                targetAI.ApplySuppression(suppressionAmount);
            }

            // Report suppression event to tactical awareness
            if (TacticalAwareness.Instance != null)
            {
                TacticalAwareness.Instance.ForceAssessment(target);
            }
        }

        /// <summary>
        /// Apply area suppression from explosions
        /// </summary>
        public void ApplyExplosionSuppression(Vector3 explosionCenter, float radius)
        {
            // Find all units in explosion radius
            Collider[] colliders = Physics.OverlapSphere(explosionCenter, radius);

            foreach (Collider col in colliders)
            {
                Unit unit = col.GetComponent<Unit>();
                if (unit != null)
                {
                    AI unitAI = unit.GetComponent<AI>();
                    if (unitAI != null)
                    {
                        // Calculate suppression based on distance from explosion
                        float distance = Vector3.Distance(unit.transform.position, explosionCenter);
                        float falloff = 1f - (distance / radius);
                        float suppressionAmount = 50f * falloff; // Max 50 suppression at center

                        unitAI.ApplySuppression(suppressionAmount);
                    }

                    // Force tactical reassessment for all affected units
                    if (TacticalAwareness.Instance != null)
                    {
                        TacticalAwareness.Instance.ForceAssessment(unit);
                    }
                }
            }
        }

        #endregion

        #region Object Pooling

        /// <summary>
        /// Return a projectile to the pool
        /// </summary>
        public void ReturnProjectileToPool(GameObject projectileObj)
        {
            if (poolManager != null)
            {
                poolManager.ReturnProjectileToPool(projectileObj);
            }
            else
            {
                // Fallback if pool manager is not available
                Destroy(projectileObj);
            }
        }

        #endregion
    }

    // WeaponData class removed - now using Weapon ScriptableObjects only

    /// <summary>
    /// Types of weapons available
    /// </summary>
    public enum WeaponType
    {
        Ballistic,      // Bullets, shells, standard projectiles
        Energy,         // Lasers, plasma, hitscan weapons
        Explosive,      // Rockets, grenades, area damage
        Guided,         // Guided missiles, homing projectiles
        Artillery,      // Long-range ballistic weapons
        AntiAir,        // Anti-aircraft weapons with lead calculation
        Melee          // Close combat weapons
    }
}
