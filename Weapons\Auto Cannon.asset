%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7eb10c3945e5dee4fa6010402875afbf, type: 3}
  m_Name: Auto Cannon
  m_EditorClassIdentifier: Assembly-CSharp::ColdVor.RTS.Weapon
  weaponName: Auto Cannon
  weaponType: 0
  description: 
  damage: 25
  range: 150
  fireRate: 0.5
  accuracy: 0.95
  projectileSpeed: 200
  projectilePrefab: {fileID: 0}
  useGravity: 0
  isHoming: 0
  isExplosive: 1
  explosionRadius: 3
  penetration: 1
  magazineSize: 35
  reloadTime: 10
  requiresAmmo: 1
  fireSound: {fileID: 0}
  reloadSound: {fileID: 0}
  dryFireSound: {fileID: 0}
  muzzleFlashPrefab: {fileID: 0}
  hitEffectPrefab: {fileID: 0}
  explosionEffectPrefab: {fileID: 0}
  spread: 0
  burstCount: 1
  burstDelay: 0.1
  canFireWhileMoving: 1
  movementAccuracyPenalty: 0.1
