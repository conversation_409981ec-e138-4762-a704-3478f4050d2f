using UnityEngine;
using System.Collections.Generic;
using System;

namespace ColdVor.RTS
{
    /// <summary>
    /// Robust projectile pool manager with type safety and comprehensive validation.
    /// Fixes critical issues identified in the pooling audit.
    /// </summary>
    public class ProjectilePoolManager : MonoBehaviour
    {
        [Header("Pool Configuration")]
        [SerializeField] private int maxPoolSizePerType = 20;
        [SerializeField] private int maxTotalPoolSize = 100;
        [SerializeField] private bool enablePoolHealthMonitoring = true;
        [SerializeField] private bool enableValidation = true;
        
        // Typed pools - separate pool for each prefab type
        private Dictionary<GameObject, Queue<GameObject>> typedPools = new Dictionary<GameObject, Queue<GameObject>>();
        private Dictionary<GameObject, PoolHealthData> poolHealthData = new Dictionary<GameObject, PoolHealthData>();
        
        // Active projectile tracking
        private HashSet<Projectile> activeProjectiles = new HashSet<Projectile>();
        
        // Events
        public event Action<GameObject, PoolHealthData> OnPoolHealthUpdated;
        public event Action<string> OnPoolWarning;
        
        private void Update()
        {
            CleanupInactiveProjectiles();
        }
        
        /// <summary>
        /// Get a pooled projectile of the specified type
        /// </summary>
        public GameObject GetPooledProjectile(GameObject prefab, Vector3 position, Quaternion rotation)
        {
            if (prefab == null)
            {
                LogPoolWarning("Attempted to get pooled projectile with null prefab");
                return null;
            }
            
            // Ensure pool exists for this prefab type
            if (!typedPools.ContainsKey(prefab))
            {
                typedPools[prefab] = new Queue<GameObject>();
                poolHealthData[prefab] = new PoolHealthData();
            }
            
            Queue<GameObject> pool = typedPools[prefab];
            PoolHealthData healthData = poolHealthData[prefab];
            GameObject projectileObj = null;
            
            // Try to get from pool
            while (pool.Count > 0)
            {
                projectileObj = pool.Dequeue();
                
                // Validate pooled object
                if (ValidatePooledProjectile(projectileObj, prefab))
                {
                    // Successfully got valid pooled projectile
                    SetupPooledProjectile(projectileObj, position, rotation);
                    healthData.LogPoolHit();
                    break;
                }
                else
                {
                    // Invalid pooled object, destroy it
                    if (projectileObj != null)
                    {
                        Destroy(projectileObj);
                    }
                    projectileObj = null;
                }
            }
            
            // If no valid pooled object found, create new one
            if (projectileObj == null)
            {
                projectileObj = CreateNewProjectile(prefab, position, rotation);
                healthData.LogPoolMiss();
            }
            
            // Track active projectile
            if (projectileObj != null)
            {
                Projectile projectileComponent = projectileObj.GetComponent<Projectile>();
                if (projectileComponent != null)
                {
                    activeProjectiles.Add(projectileComponent);
                }
            }
            
            // Update health monitoring
            if (enablePoolHealthMonitoring)
            {
                OnPoolHealthUpdated?.Invoke(prefab, healthData);
            }
            
            return projectileObj;
        }
        
        /// <summary>
        /// Return a projectile to the appropriate pool
        /// </summary>
        public void ReturnProjectileToPool(GameObject projectileObj)
        {
            if (projectileObj == null) return;
            
            // Remove from active tracking
            Projectile projectileComponent = projectileObj.GetComponent<Projectile>();
            if (projectileComponent != null)
            {
                activeProjectiles.Remove(projectileComponent);
            }
            
            // Find the appropriate pool for this projectile type
            GameObject prefabType = GetPrefabTypeForProjectile(projectileObj);
            if (prefabType == null)
            {
                // Can't determine type, just destroy
                Destroy(projectileObj);
                return;
            }
            
            // Ensure pool exists
            if (!typedPools.ContainsKey(prefabType))
            {
                typedPools[prefabType] = new Queue<GameObject>();
            }
            
            Queue<GameObject> pool = typedPools[prefabType];
            
            // Check pool size limits
            if (pool.Count >= maxPoolSizePerType || GetTotalPoolSize() >= maxTotalPoolSize)
            {
                // Pool is full, destroy the object
                Destroy(projectileObj);
                return;
            }
            
            // Reset projectile state
            if (projectileComponent != null)
            {
                projectileComponent.ResetProjectile();
            }
            
            // Deactivate and return to pool
            projectileObj.SetActive(false);
            pool.Enqueue(projectileObj);
        }
        
        /// <summary>
        /// Validate that a pooled projectile is compatible with the requested prefab
        /// </summary>
        private bool ValidatePooledProjectile(GameObject pooledObj, GameObject requestedPrefab)
        {
            if (!enableValidation) return true;
            if (pooledObj == null || requestedPrefab == null) return false;
            
            // Check if object is still valid
            if (pooledObj.Equals(null)) return false;
            
            // Check component compatibility
            Projectile pooledProjectile = pooledObj.GetComponent<Projectile>();
            Projectile prefabProjectile = requestedPrefab.GetComponent<Projectile>();
            
            if (pooledProjectile == null || prefabProjectile == null) return false;
            
            // Check for critical component mismatches
            TrailRenderer pooledTrail = pooledObj.GetComponent<TrailRenderer>();
            TrailRenderer prefabTrail = requestedPrefab.GetComponent<TrailRenderer>();
            
            Light pooledLight = pooledObj.GetComponent<Light>();
            Light prefabLight = requestedPrefab.GetComponent<Light>();
            
            // Both should have same component presence
            if ((pooledTrail != null) != (prefabTrail != null)) return false;
            if ((pooledLight != null) != (prefabLight != null)) return false;
            
            return true;
        }
        
        /// <summary>
        /// Setup a pooled projectile for reuse
        /// </summary>
        private void SetupPooledProjectile(GameObject projectileObj, Vector3 position, Quaternion rotation)
        {
            projectileObj.transform.position = position;
            projectileObj.transform.rotation = rotation;
            
            // Reset projectile state
            Projectile projectile = projectileObj.GetComponent<Projectile>();
            if (projectile != null)
            {
                projectile.ResetProjectile();
            }
        }
        
        /// <summary>
        /// Create a new projectile instance
        /// </summary>
        private GameObject CreateNewProjectile(GameObject prefab, Vector3 position, Quaternion rotation)
        {
            GameObject newProjectile = Instantiate(prefab, position, rotation);
            newProjectile.SetActive(false); // Start inactive for initialization
            return newProjectile;
        }
        
        /// <summary>
        /// Get the prefab type for a projectile (for pool categorization)
        /// </summary>
        private GameObject GetPrefabTypeForProjectile(GameObject projectileObj)
        {
            // This is a simplified approach - in a real implementation,
            // you might store the prefab reference in the projectile component
            // or use a more sophisticated type identification system
            
            foreach (var kvp in typedPools)
            {
                GameObject prefabType = kvp.Key;
                
                // Compare component signatures
                if (HasSameComponentSignature(projectileObj, prefabType))
                {
                    return prefabType;
                }
            }
            
            return null;
        }
        
        /// <summary>
        /// Check if two GameObjects have the same component signature
        /// </summary>
        private bool HasSameComponentSignature(GameObject obj1, GameObject obj2)
        {
            if (obj1 == null || obj2 == null) return false;
            
            // Compare key components
            bool obj1HasTrail = obj1.GetComponent<TrailRenderer>() != null;
            bool obj2HasTrail = obj2.GetComponent<TrailRenderer>() != null;
            
            bool obj1HasLight = obj1.GetComponent<Light>() != null;
            bool obj2HasLight = obj2.GetComponent<Light>() != null;
            
            return obj1HasTrail == obj2HasTrail && obj1HasLight == obj2HasLight;
        }
        
        /// <summary>
        /// Clean up inactive projectiles from tracking
        /// </summary>
        private void CleanupInactiveProjectiles()
        {
            activeProjectiles.RemoveWhere(p => p == null || !p.gameObject.activeInHierarchy);
        }
        
        /// <summary>
        /// Get total number of pooled objects across all pools
        /// </summary>
        private int GetTotalPoolSize()
        {
            int total = 0;
            foreach (var pool in typedPools.Values)
            {
                total += pool.Count;
            }
            return total;
        }
        
        /// <summary>
        /// Log a pool warning
        /// </summary>
        private void LogPoolWarning(string message)
        {
            Debug.LogWarning($"ProjectilePoolManager: {message}");
            OnPoolWarning?.Invoke(message);
        }
        
        /// <summary>
        /// Get pool statistics for debugging
        /// </summary>
        public PoolStatistics GetPoolStatistics()
        {
            return new PoolStatistics
            {
                TotalPooled = GetTotalPoolSize(),
                ActiveProjectiles = activeProjectiles.Count,
                PoolTypes = typedPools.Count,
                HealthData = new Dictionary<GameObject, PoolHealthData>(poolHealthData)
            };
        }
        
        /// <summary>
        /// Clear all pools (for cleanup)
        /// </summary>
        public void ClearAllPools()
        {
            foreach (var pool in typedPools.Values)
            {
                while (pool.Count > 0)
                {
                    GameObject obj = pool.Dequeue();
                    if (obj != null)
                    {
                        Destroy(obj);
                    }
                }
            }
            
            typedPools.Clear();
            poolHealthData.Clear();
            activeProjectiles.Clear();
        }
        
        private void OnDestroy()
        {
            ClearAllPools();
        }
    }
    
    /// <summary>
    /// Pool health monitoring data
    /// </summary>
    [System.Serializable]
    public class PoolHealthData
    {
        public int PoolHits { get; private set; }
        public int PoolMisses { get; private set; }
        public float HitRatio => PoolHits + PoolMisses > 0 ? PoolHits / (float)(PoolHits + PoolMisses) : 0f;
        
        public void LogPoolHit() => PoolHits++;
        public void LogPoolMiss() => PoolMisses++;
        
        public void Reset()
        {
            PoolHits = 0;
            PoolMisses = 0;
        }
    }
    
    /// <summary>
    /// Pool statistics for debugging and monitoring
    /// </summary>
    public struct PoolStatistics
    {
        public int TotalPooled;
        public int ActiveProjectiles;
        public int PoolTypes;
        public Dictionary<GameObject, PoolHealthData> HealthData;
    }
}
