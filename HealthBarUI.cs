using UnityEngine;
using UnityEngine.UI;

namespace ColdVor.RTS
{
    /// <summary>
    /// Clean health bar UI that appears above units
    /// </summary>
    public class HealthBarUI : MonoBehaviour
    {
        [Header("Health Bar Settings")]
        [SerializeField] private float barWidth = 2.42f;
        [SerializeField] private float barHeight = 0.242f;
        [SerializeField] private float heightOffset = 4.4f;
        [SerializeField] private bool showWhenFull = false;
        [SerializeField] private bool alwaysShow = false;

        [Header("Colors")]
        [SerializeField] private Color healthColor = Color.green;
        [SerializeField] private Color damageColor = Color.red;
        [SerializeField] private Color backgroundColor = new Color(0.2f, 0.2f, 0.2f, 0.8f);
        [SerializeField] private Color borderColor = new Color(0.1f, 0.1f, 0.1f, 1f);

        // Components
        private GameObject healthBarContainer;
        private Unit unit;
        private Camera mainCamera;

        // State
        private float currentHealthPercent = 1f;
        private bool isVisible = false;

        private void Awake()
        {
            unit = GetComponent<Unit>();
            if (unit == null)
            {
                DebugManager.Instance?.LogError("HealthBar", "No Unit component found");
                Destroy(this);
                return;
            }

            CreateHealthBarUI();
            
            // Subscribe to health changes
            unit.OnHealthChanged += OnHealthChanged;
        }

        private void Start()
        {
            mainCamera = Camera.main;
            if (mainCamera == null)
                mainCamera = FindFirstObjectByType<Camera>();

            // Set initial health
            UpdateHealthBar();
        }

        private void CreateHealthBarUI()
        {
            // Create container as world space object (not UI)
            healthBarContainer = new GameObject($"HealthBar_{unit.name}");
            healthBarContainer.transform.SetParent(transform);

            // Position above the unit (local space so it follows unit movement)
            healthBarContainer.transform.localPosition = Vector3.up * heightOffset;

            // Create background (red) using 3D cube
            GameObject backgroundGO = GameObject.CreatePrimitive(PrimitiveType.Cube);
            backgroundGO.name = "HealthBarBackground";
            backgroundGO.transform.SetParent(healthBarContainer.transform);
            backgroundGO.transform.localPosition = Vector3.zero;
            backgroundGO.transform.localScale = new Vector3(barWidth, barHeight, 0.1f);

            // Remove collider and set red material
            Destroy(backgroundGO.GetComponent<Collider>());
            Renderer bgRenderer = backgroundGO.GetComponent<Renderer>();
            bgRenderer.material = new Material(Shader.Find("Unlit/Color"));
            bgRenderer.material.color = damageColor;

            // Create health fill (green) using 3D cube
            GameObject healthGO = GameObject.CreatePrimitive(PrimitiveType.Cube);
            healthGO.name = "HealthBarFill";
            healthGO.transform.SetParent(healthBarContainer.transform);
            healthGO.transform.localPosition = Vector3.forward * -0.05f; // Slightly in front
            healthGO.transform.localScale = new Vector3(barWidth, barHeight, 0.05f);

            // Remove collider and set green material
            Destroy(healthGO.GetComponent<Collider>());
            Renderer fillRenderer = healthGO.GetComponent<Renderer>();
            fillRenderer.material = new Material(Shader.Find("Unlit/Color"));
            fillRenderer.material.color = healthColor;

            // Initially hidden
            healthBarContainer.SetActive(false);
        }

        private void LateUpdate()
        {
            if (healthBarContainer != null && isVisible && mainCamera != null)
            {
                UpdatePosition();
            }
        }

        private void UpdatePosition()
        {
            if (healthBarContainer == null || mainCamera == null) return;

            // Health bar position automatically follows unit since it's parented
            // Just make health bar face camera
            Vector3 directionToCamera = mainCamera.transform.position - healthBarContainer.transform.position;
            directionToCamera.y = 0; // Keep it horizontal

            if (directionToCamera != Vector3.zero)
            {
                healthBarContainer.transform.rotation = Quaternion.LookRotation(-directionToCamera);
            }
        }

        private void OnHealthChanged(Unit changedUnit, int newHealth)
        {
            if (changedUnit != unit) return;
            UpdateHealthBar();
        }

        private void UpdateHealthBar()
        {
            if (unit == null || healthBarContainer == null) return;

            currentHealthPercent = (float)unit.CurrentHealth / unit.MaxHealth;

            // Find the health fill object
            Transform healthFill = healthBarContainer.transform.Find("HealthBarFill");
            if (healthFill != null)
            {
                // Update scale to show health percentage
                Vector3 fillScale = new Vector3(barWidth * currentHealthPercent, barHeight, 0.05f);
                healthFill.localScale = fillScale;

                // Offset position so it shrinks from right to left
                float offset = (barWidth * (1f - currentHealthPercent)) * 0.5f;
                healthFill.localPosition = new Vector3(-offset, 0, -0.05f);

                // Color interpolation from red to green
                Color currentColor = Color.Lerp(damageColor, healthColor, currentHealthPercent);
                Renderer fillRenderer = healthFill.GetComponent<Renderer>();
                if (fillRenderer != null)
                {
                    fillRenderer.material.color = currentColor;
                }
            }

            // Show/hide logic - always show for testing
            bool shouldShow = true; // Always visible for now

            if (shouldShow != isVisible)
            {
                SetVisible(shouldShow);
            }
        }

        private void SetVisible(bool visible)
        {
            isVisible = visible;
            if (healthBarContainer != null)
            {
                healthBarContainer.SetActive(visible);
            }
        }

        private void OnDestroy()
        {
            if (unit != null)
            {
                unit.OnHealthChanged -= OnHealthChanged;
            }

            if (healthBarContainer != null)
            {
                Destroy(healthBarContainer);
            }
        }

        #region Public Methods
        
        public void SetAlwaysVisible(bool alwaysVisible)
        {
            alwaysShow = alwaysVisible;
            UpdateHealthBar();
        }

        public void SetShowWhenFull(bool showFull)
        {
            showWhenFull = showFull;
            UpdateHealthBar();
        }

        #endregion

        #region Editor Helpers

        [ContextMenu("Test Damage")]
        private void TestDamage()
        {
            if (unit != null && Application.isPlaying)
            {
                unit.TakeDamage(10, null);
            }
        }

        [ContextMenu("Test Heal")]
        private void TestHeal()
        {
            if (unit != null && Application.isPlaying)
            {
                unit.Heal(10);
            }
        }

        #endregion

        #region Static Setup Methods

        /// <summary>
        /// Add health bar to a unit if it doesn't already have one
        /// </summary>
        public static HealthBarUI AddToUnit(Unit unit, bool alwaysVisible = false, bool showWhenFull = false)
        {
            if (unit == null) return null;

            // Check if already has health bar
            HealthBarUI existing = unit.GetComponent<HealthBarUI>();
            if (existing != null) return existing;

            // Add health bar component
            HealthBarUI healthBar = unit.gameObject.AddComponent<HealthBarUI>();
            healthBar.alwaysShow = alwaysVisible;
            healthBar.showWhenFull = showWhenFull;

            return healthBar;
        }

        /// <summary>
        /// Add health bars to all units in the scene
        /// </summary>
        [ContextMenu("Add Health Bars to All Units")]
        public static void AddToAllUnits()
        {
            Unit[] allUnits = FindObjectsByType<Unit>(FindObjectsSortMode.None);
            int added = 0;

            foreach (Unit unit in allUnits)
            {
                if (unit.GetComponent<HealthBarUI>() == null)
                {
                    AddToUnit(unit);
                    added++;
                }
            }

            DebugManager.Instance?.LogSystem("HealthBar", $"Added health bars to {added} units");
        }

        #endregion
    }
}
