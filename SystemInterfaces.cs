using UnityEngine;
using System;
using System.Collections.Generic;

namespace ColdVor.RTS
{
    /// <summary>
    /// Core interfaces for system interaction and loose coupling
    /// </summary>
    
    #region Core System Interfaces
    
    /// <summary>
    /// Interface for objects that can be targeted by weapons
    /// </summary>
    public interface ITargetable
    {
        Vector3 Position { get; }
        Vector3 TargetPoint { get; } // Specific point to aim at (e.g., center of mass)
        bool IsValidTarget { get; }
        Faction Faction { get; }
        bool IsAlive { get; }
        float GetTargetRadius(); // For area calculations
    }
    
    /// <summary>
    /// Interface for weapon platforms (units with weapons)
    /// </summary>
    public interface IWeaponPlatform
    {
        Vector3 FirePosition { get; }
        Vector3 Forward { get; }
        bool CanEngageTarget(ITargetable target);
        WeaponSystem WeaponSystem { get; }
        Unit Unit { get; }
        bool IsMoving { get; }
    }
    
    /// <summary>
    /// Interface for vision systems
    /// </summary>
    public interface IVisionProvider
    {
        float VisionRange { get; }
        float FieldOfView { get; }
        IReadOnlyList<Unit> VisibleEnemies { get; }
        IReadOnlyList<Unit> VisibleFriendlies { get; }
        bool CanSee(ITargetable target);
        bool HasLineOfSight(Vector3 position);
        Unit GetNearestEnemy();
    }
    
    /// <summary>
    /// Interface for movement systems
    /// </summary>
    public interface IMovementProvider
    {
        bool IsMoving { get; }
        Vector3 Velocity { get; }
        Vector3 Destination { get; }
        bool MoveTo(Vector3 destination);
        void Stop();
        float MovementSpeed { get; }
    }
    
    /// <summary>
    /// Interface for health systems
    /// </summary>
    public interface IHealthProvider
    {
        int CurrentHealth { get; }
        int MaxHealth { get; }
        bool IsAlive { get; }
        bool IsDead { get; }
        float HealthPercentage { get; }
        void TakeDamage(int damage, Unit attacker = null);
        void Heal(int amount);
    }
    
    /// <summary>
    /// Interface for combat systems
    /// </summary>
    public interface ICombatProvider
    {
        void FireProjectile(IWeaponPlatform platform, ITargetable target, ProjectileParameters parameters, GameObject prefab);
        void FireInstantHit(IWeaponPlatform platform, ITargetable target, ProjectileParameters parameters);
        void CreateExplosion(Vector3 position, float radius, int damage, Unit attacker);
        void ApplyDamage(Unit attacker, Unit target, int damage, Vector3 hitPoint);
    }
    
    #endregion
    
    #region System Events
    
    /// <summary>
    /// Events for weapon system interactions
    /// </summary>
    public static class WeaponSystemEvents
    {
        public static event Action<IWeaponPlatform, ITargetable> OnWeaponFired;
        public static event Action<IWeaponPlatform> OnWeaponReloaded;
        public static event Action<IWeaponPlatform> OnAmmoEmpty;
        public static event Action<IWeaponPlatform, WeaponState> OnWeaponStateChanged;
        public static event Action<IWeaponPlatform, ITargetable> OnTargetAcquired;
        public static event Action<IWeaponPlatform, ITargetable> OnTargetLost;
        
        public static void TriggerWeaponFired(IWeaponPlatform platform, ITargetable target)
            => OnWeaponFired?.Invoke(platform, target);
            
        public static void TriggerWeaponReloaded(IWeaponPlatform platform)
            => OnWeaponReloaded?.Invoke(platform);
            
        public static void TriggerAmmoEmpty(IWeaponPlatform platform)
            => OnAmmoEmpty?.Invoke(platform);
            
        public static void TriggerWeaponStateChanged(IWeaponPlatform platform, WeaponState state)
            => OnWeaponStateChanged?.Invoke(platform, state);
            
        public static void TriggerTargetAcquired(IWeaponPlatform platform, ITargetable target)
            => OnTargetAcquired?.Invoke(platform, target);
            
        public static void TriggerTargetLost(IWeaponPlatform platform, ITargetable target)
            => OnTargetLost?.Invoke(platform, target);
    }
    
    /// <summary>
    /// Events for vision system interactions
    /// </summary>
    public static class VisionSystemEvents
    {
        public static event Action<IVisionProvider, ITargetable> OnEnemyDetected;
        public static event Action<IVisionProvider, ITargetable> OnEnemyLost;
        public static event Action<IVisionProvider, ITargetable> OnFriendlyDetected;
        public static event Action<IVisionProvider, ITargetable> OnFriendlyLost;
        public static event Action<IVisionProvider, ITargetable> OnTargetSpotted;
        
        public static void TriggerEnemyDetected(IVisionProvider provider, ITargetable target)
            => OnEnemyDetected?.Invoke(provider, target);
            
        public static void TriggerEnemyLost(IVisionProvider provider, ITargetable target)
            => OnEnemyLost?.Invoke(provider, target);
            
        public static void TriggerFriendlyDetected(IVisionProvider provider, ITargetable target)
            => OnFriendlyDetected?.Invoke(provider, target);
            
        public static void TriggerFriendlyLost(IVisionProvider provider, ITargetable target)
            => OnFriendlyLost?.Invoke(provider, target);
            
        public static void TriggerTargetSpotted(IVisionProvider provider, ITargetable target)
            => OnTargetSpotted?.Invoke(provider, target);
    }
    
    /// <summary>
    /// Events for movement system interactions
    /// </summary>
    public static class MovementSystemEvents
    {
        public static event Action<IMovementProvider> OnMovementStarted;
        public static event Action<IMovementProvider> OnMovementStopped;
        public static event Action<IMovementProvider, Vector3> OnDestinationReached;
        public static event Action<IMovementProvider> OnMovementBlocked;
        
        public static void TriggerMovementStarted(IMovementProvider provider)
            => OnMovementStarted?.Invoke(provider);
            
        public static void TriggerMovementStopped(IMovementProvider provider)
            => OnMovementStopped?.Invoke(provider);
            
        public static void TriggerDestinationReached(IMovementProvider provider, Vector3 destination)
            => OnDestinationReached?.Invoke(provider, destination);
            
        public static void TriggerMovementBlocked(IMovementProvider provider)
            => OnMovementBlocked?.Invoke(provider);
    }
    
    /// <summary>
    /// Events for health system interactions
    /// </summary>
    public static class HealthSystemEvents
    {
        public static event Action<IHealthProvider, int, int> OnHealthChanged; // provider, oldHealth, newHealth
        public static event Action<IHealthProvider, int, Unit> OnDamageTaken; // provider, damage, attacker
        public static event Action<IHealthProvider, int> OnHealed; // provider, amount
        public static event Action<IHealthProvider> OnDeath;
        public static event Action<IHealthProvider> OnRevived;
        
        public static void TriggerHealthChanged(IHealthProvider provider, int oldHealth, int newHealth)
            => OnHealthChanged?.Invoke(provider, oldHealth, newHealth);
            
        public static void TriggerDamageTaken(IHealthProvider provider, int damage, Unit attacker)
            => OnDamageTaken?.Invoke(provider, damage, attacker);
            
        public static void TriggerHealed(IHealthProvider provider, int amount)
            => OnHealed?.Invoke(provider, amount);
            
        public static void TriggerDeath(IHealthProvider provider)
            => OnDeath?.Invoke(provider);
            
        public static void TriggerRevived(IHealthProvider provider)
            => OnRevived?.Invoke(provider);
    }
    
    /// <summary>
    /// Events for combat system interactions
    /// </summary>
    public static class CombatSystemEvents
    {
        public static event Action<Unit, Unit, int> OnDamageDealt;
        public static event Action<Unit> OnUnitKilled;
        public static event Action<Vector3, float> OnExplosion;
        public static event Action<Vector3, ITargetable> OnProjectileHit;
        public static event Action<IWeaponPlatform, ITargetable> OnCombatEngaged;
        public static event Action<IWeaponPlatform, ITargetable> OnCombatDisengaged;
        
        public static void TriggerDamageDealt(Unit attacker, Unit target, int damage)
            => OnDamageDealt?.Invoke(attacker, target, damage);
            
        public static void TriggerUnitKilled(Unit unit)
            => OnUnitKilled?.Invoke(unit);
            
        public static void TriggerExplosion(Vector3 position, float radius)
            => OnExplosion?.Invoke(position, radius);
            
        public static void TriggerProjectileHit(Vector3 position, ITargetable target)
            => OnProjectileHit?.Invoke(position, target);
            
        public static void TriggerCombatEngaged(IWeaponPlatform platform, ITargetable target)
            => OnCombatEngaged?.Invoke(platform, target);
            
        public static void TriggerCombatDisengaged(IWeaponPlatform platform, ITargetable target)
            => OnCombatDisengaged?.Invoke(platform, target);
    }
    
    #endregion
    
    #region System Communication Helpers
    
    /// <summary>
    /// Helper class for system communication and coordination
    /// </summary>
    public static class SystemCommunication
    {
        /// <summary>
        /// Register a weapon platform for system interactions
        /// </summary>
        public static void RegisterWeaponPlatform(IWeaponPlatform platform)
        {
            // Subscribe to relevant events
            if (platform.WeaponSystem != null)
            {
                // Connect weapon system to vision system if available
                if (platform.Unit.GetComponent<VisionSystem>() is IVisionProvider visionProvider)
                {
                    VisionSystemEvents.OnEnemyDetected += (provider, target) =>
                    {
                        if (provider == visionProvider)
                        {
                            WeaponSystemEvents.TriggerTargetAcquired(platform, target);
                        }
                    };
                    
                    VisionSystemEvents.OnEnemyLost += (provider, target) =>
                    {
                        if (provider == visionProvider)
                        {
                            WeaponSystemEvents.TriggerTargetLost(platform, target);
                        }
                    };
                }
            }
        }
        
        /// <summary>
        /// Unregister a weapon platform from system interactions
        /// </summary>
        public static void UnregisterWeaponPlatform(IWeaponPlatform platform)
        {
            // Unsubscribe from events - implementation would depend on specific event management
            // This is a simplified example
        }
    }
    
    #endregion
}
