using UnityEngine;
using UnityEngine.AI;
using System.Collections.Generic;
using System.Linq;

namespace ColdVor.RTS
{
    /// <summary>
    /// Base class for all selectable units in the RTS system
    /// Implements ISelectable and provides common unit functionality
    /// </summary>
    [RequireComponent(typeof(Collider))]
    public class Unit : MonoBehaviour, ISelectable, ITargetable, IWeaponPlatform
    {
        [Header("Unit Properties")]
        [SerializeField] private string unitName = "Unit";
        [SerializeField] private UnitType unitType = UnitType.Infantry;
        [SerializeField] private Faction faction = Faction.Player;
        [SerializeField] private int maxHealth = 100;
        [SerializeField] private float moveSpeed = 5f;
        private float originalNavSpeed; // Store original speed

        [Header("Weapon Configuration")]
        [SerializeField] private Weapon weapon;
        [SerializeField] private bool canAttack = true;
        [SerializeField] private bool autoAcquireTargets = true;

        [Head<PERSON>("Vision & Communication")]
        [SerializeField] private float visionRange = 35f; // How far the unit can see
        [SerializeField] private float detectionRange = 30f; // How far the unit can detect enemies
        [SerializeField] private float communicationRange = 50f; // How far the unit can communicate with allies
        [SerializeField] private float fieldOfViewAngle = 120f;
        [SerializeField] private LayerMask detectionLayerMask = -1;
        // useLineOfSight removed - now handled by VisionSystem
        [SerializeField] private LayerMask visionBlockingLayers = 1; // Only terrain blocks vision, not units



        [Header("Selection")]
        [SerializeField] private GameObject selectionRing;
        [SerializeField] private bool canBeSelected = true;

        [Header("Movement")]
        [SerializeField] private float stoppingDistance = 0.1f;
        [SerializeField] private float rotationSpeed = .1f;
        [SerializeField] private float minTurnAngleForSlowdown = 15f; // Start slowing at this angle
        [SerializeField] private float maxTurnAngleForStop = 45f; // Stop completely at this angle
        [SerializeField] private float turnSpeedMultiplier = 0.2f; // Speed when turning

        [Header("Terrain Alignment")]
        [SerializeField] private bool alignToTerrain = true;
        [SerializeField] private float alignmentSpeed = 10f;
        [SerializeField] private float maxTiltAngle = 45f; // Maximum tilt before clamping
        [SerializeField] private LayerMask groundLayerMask = 1;

        [Header("Health Bar")]
        [SerializeField] private bool showHealthBar = true;

        // Private fields
        private bool isSelected = false;
        private int currentHealth;
        private bool isDead = false;
        private NavMeshAgent navAgent;
        private Collider unitCollider;
        private Quaternion targetYRotation;

        // Visual system fields
        private Renderer[] renderers;
        private Renderer[] visualRenderers; // Cached visual mesh renderers to avoid expensive GetComponentsInChildren calls
        private Transform visualMesh; // Separate visual representation

        // Modular system components
        private WeaponSystem weaponSystem;
        private VisionSystem visionSystem;
        private MovementSystem movementSystem;
        private HealthSystem healthSystem;

        // Combat fields
        private float lastAttackTime;
        private Unit currentTarget;
        private bool isInCombat = false;

        // Advanced command fields
        private bool isAttackMove = false;
        private Vector3 attackMoveTarget;

        // Detection fields
        private List<Unit> detectedEnemies = new List<Unit>();
        private List<Unit> detectedFriendlies = new List<Unit>();
        private float lastDetectionUpdate;

        // Properties implementing ISelectable
        public bool IsSelected => isSelected;
        public GameObject GameObject => gameObject;
        public Transform Transform => transform;

        // Unit properties
        public string UnitName => unitName;
        public UnitType UnitType => unitType;
        public Faction Faction => faction;

        // Health properties - delegate to HealthSystem
        public int CurrentHealth => healthSystem?.CurrentHealth ?? currentHealth;
        public int MaxHealth => healthSystem?.MaxHealth ?? maxHealth;
        public bool IsDead => healthSystem?.IsDead ?? isDead;
        public bool IsAlive => healthSystem?.IsAlive ?? !isDead;
        public float HealthPercentage => healthSystem?.HealthPercentage ?? (maxHealth > 0 ? (float)currentHealth / maxHealth : 0f);

        // Movement properties - delegate to MovementSystem
        public float MoveSpeed => movementSystem?.MovementSpeed ?? moveSpeed;
        public bool CanMove => movementSystem != null && !IsDead;
        public bool IsMoving => movementSystem?.IsMoving ?? false;
        public NavMeshAgent NavAgent => navAgent;

        // Weapon properties (for WeaponSystem to read)
        public Weapon UnitWeapon => weapon;
        public bool CanAttack => canAttack && !isDead;
        public bool AutoAcquireTargets => autoAcquireTargets;

        // Unit Vision & Communication properties
        public float VisionRange => visionSystem?.VisionRange ?? visionRange;
        public float DetectionRange => detectionRange;
        public float CommunicationRange => communicationRange;

        // Combat properties - delegate to WeaponSystem
        public float WeaponRange => weaponSystem?.WeaponRange ?? 0f;
        public int WeaponDamage => weaponSystem?.CurrentWeapon?.Damage ?? 0;
        public float FireRate => weaponSystem?.CurrentWeapon?.FireRate ?? 0.5f;

        public Unit CurrentTarget => currentTarget;
        public bool IsInCombat => isInCombat && !IsDead;
        public bool IsAttackMove => isAttackMove;

        // System accessors
        public WeaponSystem WeaponSystem => weaponSystem;
        public VisionSystem VisionSystem => visionSystem;
        public MovementSystem MovementSystem => movementSystem;
        public HealthSystem HealthSystem => healthSystem;

        // Detection properties
        public float FieldOfViewAngle => fieldOfViewAngle;
        public List<Unit> DetectedEnemies => new List<Unit>(detectedEnemies);
        public List<Unit> DetectedFriendlies => new List<Unit>(detectedFriendlies);



        // Events
        public System.Action<Unit> OnUnitSelected;
        public System.Action<Unit> OnUnitDeselected;
        public System.Action<Unit, int> OnHealthChanged;
        public System.Action<Unit> OnUnitDestroyed;

        // Combat events
        public System.Action<Unit, Unit> OnAttackStarted;
        public System.Action<Unit, Unit, int> OnDamageDealt;
        public System.Action<Unit, Unit> OnTargetAcquired;
        public System.Action<Unit> OnTargetLost;

        // Detection events
        public System.Action<Unit, Unit> OnEnemyDetected;
        public System.Action<Unit, Unit> OnEnemyLost;
        public System.Action<Unit, Unit> OnFriendlyDetected;

        protected virtual void Awake()
        {
            navAgent = GetComponent<NavMeshAgent>();
            unitCollider = GetComponent<Collider>();
            renderers = GetComponentsInChildren<Renderer>();

            currentHealth = maxHealth;

            // Initialize modular systems
            InitializeModularSystems();

            // Initialize faction-based settings
            InitializeFactionSettings();

            if (navAgent != null)
            {
                navAgent.speed = moveSpeed;
                originalNavSpeed = moveSpeed; // Store original speed
                navAgent.stoppingDistance = Mathf.Max(2f, stoppingDistance); // Increased stopping distance to prevent crowding
                navAgent.angularSpeed = rotationSpeed;
                navAgent.acceleration = 8f;
                navAgent.autoBraking = true;

                // Improved collision settings
                navAgent.radius = 1.5f; // Increased radius for better unit separation
                navAgent.height = 2f; // Set appropriate height
                navAgent.obstacleAvoidanceType = ObstacleAvoidanceType.HighQualityObstacleAvoidance;
                navAgent.avoidancePriority = 50; // Default priority
                navAgent.updateRotation = false; // Let us handle rotation

                // Let NavMeshAgent handle its own rotation - we'll handle visual separately
            }

             // Initialize target rotation
            targetYRotation = transform.rotation;
            // Setup visual mesh separation
            SetupVisualMesh();

            if (selectionRing == null)
            {
                CreateSelectionRing();
            }

            if (selectionRing != null)
            {
                selectionRing.SetActive(false);
            }

            // Add health bar component
            if (showHealthBar)
            {
                gameObject.AddComponent<HealthBarUI>();
            }


        }

        /// <summary>
        /// Initialize modular systems for this unit
        /// </summary>
        private void InitializeModularSystems()
        {
            // Initialize HealthSystem (always needed)
            healthSystem = GetComponent<HealthSystem>();
            if (healthSystem == null)
            {
                healthSystem = gameObject.AddComponent<HealthSystem>();
            }

            // Initialize MovementSystem (always needed)
            movementSystem = GetComponent<MovementSystem>();
            if (movementSystem == null)
            {
                movementSystem = gameObject.AddComponent<MovementSystem>();
            }

            // Initialize WeaponSystem (conditional)
            weaponSystem = GetComponent<WeaponSystem>();
            if (weaponSystem == null && canAttack)
            {
                weaponSystem = gameObject.AddComponent<WeaponSystem>();
            }

            // Initialize VisionSystem (always needed)
            visionSystem = GetComponent<VisionSystem>();
            if (visionSystem == null)
            {
                visionSystem = gameObject.AddComponent<VisionSystem>();
                // Transfer vision settings from Unit to VisionSystem
                visionSystem.SetVisionRange(visionRange);
                visionSystem.SetFieldOfView(fieldOfViewAngle);
            }

            // Subscribe to system events
            if (healthSystem != null)
            {
                healthSystem.OnHealthChanged += OnHealthSystemHealthChanged;
                healthSystem.OnDeath += OnHealthSystemDeath;
                healthSystem.OnHealthCritical += OnHealthSystemCritical;
            }

            if (movementSystem != null)
            {
                movementSystem.OnMovementStarted += OnMovementSystemStarted;
                movementSystem.OnMovementStopped += OnMovementSystemStopped;
                movementSystem.OnDestinationReached += OnMovementSystemDestinationReached;
            }

            if (weaponSystem != null)
            {
                weaponSystem.OnWeaponFired += OnWeaponSystemFired;
                weaponSystem.OnAmmoEmpty += OnWeaponSystemAmmoEmpty;
            }

            if (visionSystem != null)
            {
                visionSystem.OnEnemyDetected += OnVisionSystemEnemyDetected;
                visionSystem.OnEnemyLost += OnVisionSystemEnemyLost;
            }
        }

        protected virtual void Start()
        {
            // Initialize staggered combat updates to prevent synchronous firing
            combatUpdateOffset = (GetInstanceID() % 20) * 0.01f; // 0-0.2 second offset
            lastCombatUpdate = Time.time + combatUpdateOffset;

            // Validate all systems are properly initialized
            ValidateSystemIntegration();
        }

        /// <summary>
        /// Validate that all required systems are properly integrated
        /// </summary>
        private void ValidateSystemIntegration()
        {
            bool allSystemsValid = true;

            if (healthSystem == null)
            {
                Debug.LogError($"Unit {unitName}: HealthSystem not initialized!");
                allSystemsValid = false;
            }

            if (movementSystem == null)
            {
                Debug.LogError($"Unit {unitName}: MovementSystem not initialized!");
                allSystemsValid = false;
            }

            if (visionSystem == null)
            {
                Debug.LogError($"Unit {unitName}: VisionSystem not initialized!");
                allSystemsValid = false;
            }

            if (canAttack && weaponSystem == null)
            {
                Debug.LogError($"Unit {unitName}: WeaponSystem not initialized but canAttack is true!");
                allSystemsValid = false;
            }

            if (allSystemsValid && DebugManager.Instance != null)
            {
                DebugManager.Instance.LogSystem("Unit", $"{unitName} - All systems integrated successfully");
            }
        }

        #region System Event Handlers

        /// <summary>
        /// Handle health system health changed event
        /// </summary>
        private void OnHealthSystemHealthChanged(HealthSystem health, int oldHealth, int newHealth)
        {
            // Update unit's health tracking
            currentHealth = newHealth;

            // Trigger unit-level events
            OnHealthChanged?.Invoke(this, newHealth);

            if (DebugManager.Instance != null)
            {
                DebugManager.Instance.LogSystem("Unit", $"{unitName} health changed: {oldHealth} → {newHealth}");
            }
        }

        /// <summary>
        /// Handle health system death event
        /// </summary>
        private void OnHealthSystemDeath(HealthSystem health)
        {
            // Delegate to unit's death handling
            Die();
        }

        /// <summary>
        /// Handle health system critical health event
        /// </summary>
        private void OnHealthSystemCritical(HealthSystem health)
        {
            if (DebugManager.Instance != null)
            {
                DebugManager.Instance.LogCombat($"{unitName} health is critical!");
            }
        }

        /// <summary>
        /// Handle movement system started event
        /// </summary>
        private void OnMovementSystemStarted(MovementSystem movement)
        {
            if (DebugManager.Instance != null)
            {
                DebugManager.Instance.LogSystem("Unit", $"{unitName} started moving");
            }
        }

        /// <summary>
        /// Handle movement system stopped event
        /// </summary>
        private void OnMovementSystemStopped(MovementSystem movement)
        {
            if (DebugManager.Instance != null)
            {
                DebugManager.Instance.LogSystem("Unit", $"{unitName} stopped moving");
            }
        }

        /// <summary>
        /// Handle movement system destination reached event
        /// </summary>
        private void OnMovementSystemDestinationReached(MovementSystem movement, Vector3 destination)
        {
            if (DebugManager.Instance != null)
            {
                DebugManager.Instance.LogSystem("Unit", $"{unitName} reached destination: {destination}");
            }
        }

        /// <summary>
        /// Handle weapon system fired event
        /// </summary>
        private void OnWeaponSystemFired(WeaponSystem weapon, Unit target)
        {
            // Update combat state
            lastAttackTime = Time.time;
            isInCombat = true;

            // Trigger unit-level events
            OnAttackStarted?.Invoke(this, target);
        }

        /// <summary>
        /// Handle weapon system ammo empty event
        /// </summary>
        private void OnWeaponSystemAmmoEmpty(WeaponSystem weapon)
        {
            // Could trigger reload behavior or ammo warnings
            if (DebugManager.Instance != null)
            {
                DebugManager.Instance.LogCombat($"{gameObject.name} is out of ammo");
            }
        }

        /// <summary>
        /// Handle vision system enemy detected event
        /// </summary>
        private void OnVisionSystemEnemyDetected(VisionSystem vision, Unit enemy)
        {
            // Add to detected enemies list for compatibility
            if (!detectedEnemies.Contains(enemy))
            {
                detectedEnemies.Add(enemy);
            }

            // Trigger unit-level events
            OnEnemyDetected?.Invoke(this, enemy);
        }

        /// <summary>
        /// Handle vision system enemy lost event
        /// </summary>
        private void OnVisionSystemEnemyLost(VisionSystem vision, Unit enemy)
        {
            // Remove from detected enemies list
            detectedEnemies.Remove(enemy);

            // Trigger unit-level events
            OnEnemyLost?.Invoke(this, enemy);
        }

        #endregion

        // Staggered combat updates to prevent synchronous firing
        private float combatUpdateOffset;
        private float lastCombatUpdate;

        protected virtual void Update()
        {
            if (isDead) return;

            HandleMovement();
            // UpdateDetection(); // Disabled - now handled by VisionSystem

            // Staggered combat updates to prevent synchronous firing
            if (Time.time - lastCombatUpdate >= (0.1f + combatUpdateOffset))
            {
                UpdateCombat();
                lastCombatUpdate = Time.time;
            }

            UpdateAdvancedCommands();

            if (alignToTerrain)
            {
                AlignToTerrain();
            }

            SynchronizeNavMeshAgent();
        }

        private void UpdateAdvancedCommands()
        {
            if (isAttackMove)
            {
                HandleAttackMove();
            }
        }

        private void HandleAttackMove()
        {
            if (currentTarget == null && detectedEnemies.Count > 0)
            {
                Unit closestEnemy = GetClosestEnemy();
                if (closestEnemy != null)
                {
                    SetTarget(closestEnemy);
                }
            }
        }


        // Add this new method:
private void HandleMovement()
{
   if (navAgent == null || !navAgent.hasPath)
    {
        // Reset speed when not moving
        if (navAgent != null) navAgent.speed = originalNavSpeed;
        return;
    }

    // Calculate desired rotation based on movement direction
    Vector3 desiredVelocity = navAgent.desiredVelocity;
    if (desiredVelocity.magnitude > 0.1f)
    {
        // Calculate target rotation based on movement direction
        Vector3 lookDirection = desiredVelocity.normalized;
        lookDirection.y = 0; // Keep only horizontal rotation

        if (lookDirection != Vector3.zero)
        {
            Quaternion desiredRotation = Quaternion.LookRotation(lookDirection);
            targetYRotation = desiredRotation;
        }
    }

    // Calculate how much we need to turn
    float yawDifference = Quaternion.Angle(transform.rotation, targetYRotation);

    // Adjust movement speed based on turn angle
    float speedMultiplier = 1f;

    if (yawDifference > minTurnAngleForSlowdown)
    {
        if (yawDifference >= maxTurnAngleForStop)
        {
            // Stop completely for sharp turns
            speedMultiplier = 0f;
        }
        else
        {
            // Gradually slow down as turn angle increases
            float turnProgress = (yawDifference - minTurnAngleForSlowdown) / (maxTurnAngleForStop - minTurnAngleForSlowdown);
            speedMultiplier = Mathf.Lerp(1f, turnSpeedMultiplier, turnProgress);
        }
    }

    // Apply speed adjustment to NavMeshAgent
    navAgent.speed = originalNavSpeed * speedMultiplier;

    // Smoothly rotate towards target
    if (yawDifference > 1f) // Only rotate if there's a meaningful difference
    {
        transform.rotation = Quaternion.RotateTowards(
            transform.rotation,
            targetYRotation,
            rotationSpeed * Time.deltaTime
        );
    }
}

        private void SynchronizeNavMeshAgent()
        {
            if (navAgent == null || visualMesh == null) return;

            // Keep the NavMeshAgent's position synchronized with the main transform
            // while allowing the visual mesh to be offset for terrain alignment
            Vector3 agentPosition = transform.position;

            // Ensure the agent stays on the NavMesh
            if (UnityEngine.AI.NavMesh.SamplePosition(agentPosition, out UnityEngine.AI.NavMeshHit hit, 2f, UnityEngine.AI.NavMesh.AllAreas))
            {
                // Only update if the difference is significant to avoid jittering
                float distance = Vector3.Distance(navAgent.transform.position, hit.position);
                if (distance > 0.1f)
                {
                    navAgent.Warp(hit.position);
                }
            }
        }

        #region ISelectable Implementation

        public virtual void OnSelected()
        {
            if (!canBeSelected || isDead) return;

            isSelected = true;

            if (selectionRing != null)
            {
                selectionRing.SetActive(true);
            }

            OnUnitSelected?.Invoke(this);
        }

        public virtual void OnDeselected()
        {
            isSelected = false;

            if (selectionRing != null)
            {
                selectionRing.SetActive(false);
            }

            OnUnitDeselected?.Invoke(this);
        }

        public virtual Bounds GetSelectionBounds()
        {
            if (unitCollider == null)
                unitCollider = GetComponent<Collider>();

            if (unitCollider != null)
            {
                return unitCollider.bounds;
            }

            if (renderers == null || renderers.Length == 0)
                renderers = GetComponentsInChildren<Renderer>();

            Bounds bounds = new Bounds(transform.position, Vector3.one);
            if (renderers != null)
            {
                foreach (var renderer in renderers)
                {
                    if (renderer != null)
                    {
                        bounds.Encapsulate(renderer.bounds);
                    }
                }
            }

            return bounds;
        }

        public virtual bool CanBeSelected()
        {
            if (!canBeSelected || !gameObject.activeInHierarchy)
                return false;

            // Check if faction can be selected by player
            if (FactionManager.Instance != null)
            {
                return FactionManager.Instance.CanFactionBeSelectedByPlayer(faction);
            }

            // Default to allowing selection if no faction manager
            return true;
        }

        /// <summary>
        /// Unsubscribe from all system events
        /// </summary>
        private void UnsubscribeFromSystemEvents()
        {
            if (healthSystem != null)
            {
                healthSystem.OnHealthChanged -= OnHealthSystemHealthChanged;
                healthSystem.OnDeath -= OnHealthSystemDeath;
                healthSystem.OnHealthCritical -= OnHealthSystemCritical;
            }

            if (movementSystem != null)
            {
                movementSystem.OnMovementStarted -= OnMovementSystemStarted;
                movementSystem.OnMovementStopped -= OnMovementSystemStopped;
                movementSystem.OnDestinationReached -= OnMovementSystemDestinationReached;
            }

            if (weaponSystem != null)
            {
                weaponSystem.OnWeaponFired -= OnWeaponSystemFired;
                weaponSystem.OnAmmoEmpty -= OnWeaponSystemAmmoEmpty;
            }

            if (visionSystem != null)
            {
                visionSystem.OnEnemyDetected -= OnVisionSystemEnemyDetected;
                visionSystem.OnEnemyLost -= OnVisionSystemEnemyLost;
            }
        }

        #endregion

        #region Movement Methods

        public virtual bool MoveTo(Vector3 destination)
        {
            if (!CanMove) return false;

            // Adjust destination to avoid crowding friendly units
            Vector3 adjustedDestination = AdjustDestinationForUnitSpacing(destination);

            // Delegate to MovementSystem
            if (movementSystem != null)
            {
                return movementSystem.MoveTo(adjustedDestination);
            }

            // Fallback to direct NavMeshAgent (legacy support)
            if (navAgent == null) return false;

            bool result = navAgent.SetDestination(adjustedDestination);
            return result;
        }

        /// <summary>
        /// Adjust destination to maintain proper spacing from friendly units
        /// </summary>
        private Vector3 AdjustDestinationForUnitSpacing(Vector3 originalDestination)
        {
            float minSpacing = 3f; // Minimum distance between units
            Vector3 adjustedDestination = originalDestination;

            // Check for nearby friendly units at the destination
            Collider[] nearbyUnits = Physics.OverlapSphere(originalDestination, minSpacing);

            foreach (Collider col in nearbyUnits)
            {
                Unit otherUnit = col.GetComponent<Unit>();
                if (otherUnit != null && otherUnit != this && otherUnit.Faction == this.Faction && !otherUnit.IsDead)
                {
                    // Found a friendly unit too close to destination, adjust position
                    Vector3 directionAway = (originalDestination - otherUnit.transform.position).normalized;
                    adjustedDestination = otherUnit.transform.position + directionAway * minSpacing;
                    break; // Use first adjustment found
                }
            }

            return adjustedDestination;
        }



        public virtual void StopMovement()
        {
            if (navAgent != null && navAgent.enabled)
            {
                navAgent.ResetPath();
            }
        }

        #endregion

        #region Health and Combat

        public virtual void TakeDamage(int damage, Unit attacker = null)
        {
            if (damage <= 0 || IsDead) return; // Can't damage dead units

            // Delegate to HealthSystem
            if (healthSystem != null)
            {
                healthSystem.TakeDamage(damage, attacker);
                return;
            }

            // Fallback to legacy health handling
            // Check if damage should be applied based on faction relationships
            if (attacker != null && FactionManager.Instance != null)
            {
                bool shouldApply = FactionManager.Instance.ShouldApplyDamage(attacker.Faction, this.Faction);

                if (!shouldApply)
                {
                    return; // Don't apply damage due to faction relationships
                }
            }

            int oldHealth = currentHealth;
            currentHealth = Mathf.Max(0, currentHealth - damage);

            // Clean damage logging
            if (DebugManager.Instance != null)
            {
                DebugManager.Instance.LogDamage(attacker, this, damage, currentHealth);
            }

            OnHealthChanged?.Invoke(this, currentHealth);

            // Trigger damage dealt event for attacker
            if (attacker != null)
            {
                attacker.OnDamageDealt?.Invoke(attacker, this, damage);
            }

            if (currentHealth <= 0)
            {
                Die();
                DebugManager.Instance?.LogUnitDeath(this);
            }
        }

        public virtual void Heal(int amount)
        {
            if (amount <= 0) return;

            // Delegate to HealthSystem
            if (healthSystem != null)
            {
                healthSystem.Heal(amount);
                return;
            }

            // Fallback to legacy health handling
            currentHealth = Mathf.Min(maxHealth, currentHealth + amount);
            OnHealthChanged?.Invoke(this, currentHealth);
        }

        protected virtual void Die()
        {
            if (isDead) return; // Already dead

            isDead = true;

            // Unsubscribe from system events
            UnsubscribeFromSystemEvents();

            OnUnitDestroyed?.Invoke(this);

            // Remove from selection if selected
            if (isSelected)
            {
                var inputManager = RTSInputManager.Instance;
                if (inputManager != null)
                {
                    inputManager.RemoveFromSelection(this);
                }
            }

            // Disable movement and combat
            if (navAgent != null)
            {
                navAgent.enabled = false;
            }

            // Clear current target and stop combat
            currentTarget = null;
            isInCombat = false;

            // Trigger simple death effect
            TriggerDeathEffect();

            // Disable selection ring and collider immediately
            if (selectionRing != null)
            {
                selectionRing.SetActive(false);
            }
            if (unitCollider != null)
            {
                unitCollider.enabled = false;
            }

            // Unit will remain with explosion shader that transitions to grey
            // No need for separate grey material application
        }

        /// <summary>
        /// Trigger simple death effect (replaces complex AIExplosionEffect)
        /// </summary>
        private void TriggerDeathEffect()
        {
            // Simple death effect - just disable the unit after a delay
            Invoke(nameof(DestroyUnit), 2f);
        }

        /// <summary>
        /// Destroy the unit GameObject
        /// </summary>
        private void DestroyUnit()
        {
            if (gameObject != null)
            {
                Destroy(gameObject);
            }
        }





        #endregion

        #region Combat Commands

        /// <summary>
        /// Set a specific target for this unit to attack
        /// </summary>
        public void SetAttackTarget(Unit target)
        {
            if (target == null) return;

            currentTarget = target;
            isAttackMove = false;
            attackMoveTarget = Vector3.zero;

            Debug.Log($"{name} targeting {target.name} for attack");
        }

        /// <summary>
        /// Set attack-move target position
        /// </summary>
        public void SetAttackMoveTarget(Vector3 targetPosition)
        {
            attackMoveTarget = targetPosition;
            isAttackMove = true;
            currentTarget = null;

            Debug.Log($"{name} set to attack-move to {targetPosition}");
        }



        /// <summary>
        /// Get the effective weapon range for this unit
        /// </summary>
        public float GetWeaponRange()
        {
            return WeaponRange; // Delegate to property which uses WeaponSystem
        }

        #endregion

        #region Selection Ring

        private void CreateSelectionRing()
        {
            CreateMeshOutline();
        }

        private void CreateMeshOutline()
        {
            // Create outline container that follows visual mesh
            GameObject outlineContainer = new GameObject("SelectionOutline");
            outlineContainer.transform.SetParent(visualMesh != null ? visualMesh : transform);
            outlineContainer.transform.localPosition = Vector3.zero;
            outlineContainer.transform.localRotation = Quaternion.identity;
            outlineContainer.transform.localScale = Vector3.one;

            // Create outline at collider center height
            CreateColliderCenterOutline(outlineContainer.transform);

            selectionRing = outlineContainer;
        }

        private void CreateColliderCenterOutline(Transform parent)
        {
            if (unitCollider == null) return;

            GameObject outlineObj = new GameObject("ColliderOutline");
            outlineObj.transform.SetParent(parent);
            outlineObj.transform.localPosition = Vector3.zero;
            outlineObj.transform.localRotation = Quaternion.identity;

            LineRenderer lineRenderer = outlineObj.AddComponent<LineRenderer>();

            // Setup line renderer with selection ring material
            Material selectionMaterial = Resources.Load<Material>("Materials/SelectionRing");
            if (selectionMaterial == null)
            {
                selectionMaterial = new Material(Shader.Find("Unlit/Color"));
                selectionMaterial.color = new Color(0.4f, 0.7f, 0.4f, 0.8f); // Muted green
            }
            lineRenderer.material = selectionMaterial;

            lineRenderer.startWidth = 0.15f;
            lineRenderer.endWidth = 0.15f;
            lineRenderer.useWorldSpace = false;
            lineRenderer.loop = true;
            lineRenderer.numCapVertices = 5;
            lineRenderer.numCornerVertices = 5;
            lineRenderer.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.Off;
            lineRenderer.receiveShadows = false;

            // Create outline at collider center height (between min and max)
            Bounds bounds = unitCollider.bounds;
            Vector3 center = bounds.center;
            Vector3 size = bounds.size;

            // Use collider center Y (halfway between min and max)
            float centerY = center.y - transform.position.y; // Convert to local space

            Vector3[] outlinePoints = new Vector3[]
            {
                new Vector3(-size.x * 0.5f, centerY, -size.z * 0.5f), // Front-left
                new Vector3(size.x * 0.5f, centerY, -size.z * 0.5f),  // Front-right
                new Vector3(size.x * 0.5f, centerY, size.z * 0.5f),   // Back-right
                new Vector3(-size.x * 0.5f, centerY, size.z * 0.5f),  // Back-left
            };

            lineRenderer.positionCount = outlinePoints.Length;
            lineRenderer.SetPositions(outlinePoints);
        }







        #endregion

        #region Visual Mesh Setup

        private void SetupVisualMesh()
        {
            // Find or create visual mesh container
            visualMesh = transform.Find("VisualMesh");
            if (visualMesh == null)
            {
                // Create visual mesh container
                GameObject visualContainer = new GameObject("VisualMesh");
                visualContainer.transform.SetParent(transform);
                visualContainer.transform.localPosition = Vector3.zero;
                visualContainer.transform.localRotation = Quaternion.identity;
                visualContainer.transform.localScale = Vector3.one;

                // Move entire mesh hierarchies to preserve relationships
                Transform[] directChildren = new Transform[transform.childCount];
                for (int i = 0; i < transform.childCount; i++)
                {
                    directChildren[i] = transform.GetChild(i);
                }

                // Move all direct children that have renderers (preserving hierarchy)
                foreach (Transform child in directChildren)
                {
                    if (child.name != "VisualMesh" && HasRendererInHierarchy(child))
                    {

                        // Check if this child has a SkinnedMeshRenderer
                        SkinnedMeshRenderer skinnedRenderer = child.GetComponent<SkinnedMeshRenderer>();
                        if (skinnedRenderer != null)
                        {
                            HandleSkinnedMeshRenderer(skinnedRenderer, visualContainer.transform);
                        }
                        else
                        {
                            child.SetParent(visualContainer.transform);
                        }
                    }
                    else if (child.name != "VisualMesh")
                    {

                    }
                }

                visualMesh = visualContainer.transform;
            }

            // Cache visual renderers after setup
            CacheVisualRenderers();
        }

        private void CacheVisualRenderers()
        {
            if (visualMesh != null)
            {
                visualRenderers = visualMesh.GetComponentsInChildren<Renderer>();
            }
            else
            {
                visualRenderers = new Renderer[0]; // Empty array if no visual mesh
            }
        }

        /// <summary>
        /// Call this if visual components are added/removed at runtime to refresh the cache
        /// </summary>
        public void RefreshVisualRenderers()
        {
            CacheVisualRenderers();
        }

        private bool HasRendererInHierarchy(Transform obj)
        {
            // Check if this object or any of its children have renderers
            return obj.GetComponentsInChildren<Renderer>().Length > 0;
        }

        private bool IsChildOf(Transform child, Transform parent)
        {
            Transform current = child.parent;
            while (current != null)
            {
                if (current == parent)
                    return true;
                current = current.parent;
            }
            return false;
        }

        private void HandleSkinnedMeshRenderer(SkinnedMeshRenderer skinnedRenderer, Transform newParent)
        {


            // Store original bones
            Transform[] originalBones = skinnedRenderer.bones;

            // Find the root bone (the one that's not a child of any other bone in the array)
            Transform rootBone = skinnedRenderer.rootBone;
            if (rootBone == null && originalBones.Length > 0)
            {
                // If no explicit root bone, find the common ancestor
                rootBone = FindCommonAncestor(originalBones);
            }

            // Move the skinned mesh renderer
            skinnedRenderer.transform.SetParent(newParent);

            // Move the root bone hierarchy to maintain the bone relationships
            if (rootBone != null && rootBone.parent != newParent)
            {
                rootBone.SetParent(newParent);
            }


        }

        private Transform FindCommonAncestor(Transform[] bones)
        {
            if (bones.Length == 0) return null;
            if (bones.Length == 1) return bones[0];

            // Start with the first bone and walk up the hierarchy
            Transform candidate = bones[0];
            while (candidate != null)
            {
                bool isCommonAncestor = true;
                for (int i = 1; i < bones.Length; i++)
                {
                    if (!IsChildOf(bones[i], candidate) && bones[i] != candidate)
                    {
                        isCommonAncestor = false;
                        break;
                    }
                }

                if (isCommonAncestor)
                    return candidate;

                candidate = candidate.parent;
            }

            return null; // No common ancestor found
        }



private Vector3 smoothedGroundPoint = Vector3.zero; // Add this as a class field
        private Vector3 lastValidGroundPoint = Vector3.zero; // Backup ground point
        private float lastGroundCheckTime = 0f; // Timing for ground checks

        private void AlignToTerrain()
        {
            if (visualMesh == null || unitCollider == null) return;

            // Higher frequency ground checks for smoother alignment
            if (Time.time - lastGroundCheckTime < 0.01f) return; // 100 FPS for ground checks
            lastGroundCheckTime = Time.time;

            // Multiple raycast points for more robust ground detection
            Vector3 unitCenter = transform.position;
            Vector3 unitForward = transform.forward;
            Vector3 unitRight = transform.right;

            // Cast from multiple points around the unit's base
            List<RaycastHit> groundHits = new List<RaycastHit>();
            Vector3[] raycastPoints = new Vector3[]
            {
                unitCenter, // Center
                unitCenter + unitForward * 0.5f, // Front
                unitCenter - unitForward * 0.5f, // Back
                unitCenter + unitRight * 0.5f, // Right
                unitCenter - unitRight * 0.5f  // Left
            };

            Vector3 averageGroundPoint = Vector3.zero;
            Vector3 averageNormal = Vector3.zero;
            int validHits = 0;

            foreach (Vector3 rayPoint in raycastPoints)
            {
                Vector3 rayStart = rayPoint + Vector3.up * 2f; // Start above unit
                if (Physics.Raycast(rayStart, Vector3.down, out RaycastHit hit, 10f, groundLayerMask))
                {
                    // Validate hit is reasonable (not too far from expected position)
                    float heightDifference = Mathf.Abs(hit.point.y - unitCenter.y);
                    if (heightDifference < 5f) // Reasonable height difference
                    {
                        groundHits.Add(hit);
                        averageGroundPoint += hit.point;
                        averageNormal += hit.normal;
                        validHits++;
                    }
                }
            }

            // Only proceed if we have valid ground hits
            if (validHits == 0)
            {
                // Use last valid ground point as fallback
                if (lastValidGroundPoint != Vector3.zero)
                {
                    averageGroundPoint = lastValidGroundPoint;
                    averageNormal = Vector3.up;
                    validHits = 1;
                }
                else
                {
                    return; // No valid ground found, skip this frame
                }
            }
            else
            {
                averageGroundPoint /= validHits;
                averageNormal = (averageNormal / validHits).normalized;
                lastValidGroundPoint = averageGroundPoint; // Store as backup
            }

            // Smooth the ground point over time to eliminate jittering
            if (smoothedGroundPoint == Vector3.zero)
            {
                smoothedGroundPoint = averageGroundPoint;
            }
            else
            {
                // Improved smoothing - faster response when going uphill, slower when going downhill
                float heightDifference = averageGroundPoint.y - smoothedGroundPoint.y;

                // If unit is not moving and height difference is small, don't adjust to prevent floating
                bool isMoving = navAgent != null && navAgent.velocity.magnitude > 0.1f;
                if (!isMoving && Mathf.Abs(heightDifference) < 0.05f)
                {
                    return; // Don't adjust position when stationary and close to ground
                }

                float smoothingSpeed;
                if (heightDifference > 0.1f)
                {
                    smoothingSpeed = 20f; // Instant uphill to prevent clipping
                }
                else if (heightDifference < -0.1f)
                {
                    smoothingSpeed = 15f; // Silky smooth downhill
                }
                else
                {
                    smoothingSpeed = 10f; // Smooth normal response
                }

                // Silky smooth exponential interpolation
                float dampingFactor = 1f - Mathf.Exp(-smoothingSpeed * Time.deltaTime);
                smoothedGroundPoint = Vector3.Lerp(smoothedGroundPoint, averageGroundPoint, dampingFactor);
            }

            // Ensure normal points up and isn't too steep
            if (averageNormal.y < 0.3f) averageNormal = Vector3.up;

            // Limit tilt angle
            float tiltAngle = Vector3.Angle(Vector3.up, averageNormal);
            if (tiltAngle > maxTiltAngle)
            {
                float t = maxTiltAngle / tiltAngle;
                averageNormal = Vector3.Slerp(Vector3.up, averageNormal, t).normalized;
            }

            // Create rotation from normal and main transform's forward
            Vector3 forward = transform.forward;
            Vector3 right = Vector3.Cross(averageNormal, forward).normalized;
            Vector3 adjustedForward = Vector3.Cross(right, averageNormal).normalized;

            Quaternion terrainRotation = Quaternion.LookRotation(adjustedForward, averageNormal);

            // Apply terrain alignment to visual mesh with proper slope following
            // Use the main transform's Y rotation combined with terrain tilt
            Quaternion mainYRotation = Quaternion.Euler(0, transform.eulerAngles.y, 0);
            Quaternion slopeRotation = Quaternion.FromToRotation(Vector3.up, averageNormal);
            Quaternion finalRotation = slopeRotation * mainYRotation;

            // Silky smooth rotation with instant alignment capability
            float rotationDamping = 1f - Mathf.Exp(-alignmentSpeed * Time.deltaTime * 6f);
            visualMesh.rotation = Quaternion.Slerp(visualMesh.rotation, finalRotation, rotationDamping);

            // Position the unit properly on the ground with more conservative positioning
            Bounds visualBounds = GetVisualBounds();
            float bottomOffset = visualBounds.min.y - visualBounds.center.y + 2.5f; // Reduced offset

            float targetCenterY = smoothedGroundPoint.y - bottomOffset;
            float localYOffset = targetCenterY - transform.position.y;

            // Clamp the Y offset to prevent extreme movements
            localYOffset = Mathf.Clamp(localYOffset, -2f, 2f);

            Vector3 targetPos = new Vector3(0, localYOffset, 0);

            // Ultra-smooth position interpolation using exponential damping
            float positionDamping = 1f - Mathf.Exp(-alignmentSpeed * Time.deltaTime * 6f);
            visualMesh.localPosition = Vector3.Lerp(visualMesh.localPosition, targetPos, positionDamping);
        }



        private Bounds GetVisualBounds()
        {
            if (visualMesh == null) return new Bounds();

            // Use cached visual renderers instead of expensive GetComponentsInChildren call
            if (visualRenderers == null || visualRenderers.Length == 0)
            {
                CacheVisualRenderers(); // Refresh cache if needed
                if (visualRenderers.Length == 0) return new Bounds();
            }

            Bounds bounds = visualRenderers[0].bounds;
            for (int i = 1; i < visualRenderers.Length; i++)
            {
                bounds.Encapsulate(visualRenderers[i].bounds);
            }

            return bounds;
        }



        #endregion

        #region Utility Methods

        public virtual float GetDistanceTo(Vector3 position)
        {
            return RTSUtilities.GetDistance(this, position);
        }

        public virtual float GetDistanceTo(Unit otherUnit)
        {
            return RTSUtilities.GetDistance(this, otherUnit);
        }

        public virtual Vector3 GetGroundPosition()
        {
            return RTSUtilities.GetGroundPosition(transform.position, groundLayerMask);
        }

        #endregion

        #region Faction and Combat Methods



        /// <summary>
        /// Set the unit's faction (for use by managers)
        /// </summary>
        public void SetFaction(Faction newFaction)
        {
            faction = newFaction;
            InitializeFactionSettings();
        }

        /// <summary>
        /// Set the unit's weapon (for runtime weapon changes)
        /// </summary>
        public void SetWeapon(Weapon newWeapon)
        {
            weapon = newWeapon;

            // Notify WeaponSystem to refresh
            if (weaponSystem != null)
            {
                weaponSystem.RefreshWeaponData();
            }

            if (DebugManager.Instance != null)
            {
                DebugManager.Instance.LogSystem("Unit", $"{unitName} weapon changed to {newWeapon?.WeaponName ?? "None"}");
            }
        }

        /// <summary>
        /// Initialize faction-based settings from FactionManager
        /// </summary>
        private void InitializeFactionSettings()
        {
            // Faction-specific property setting removed - each unit has its own detection/communication ranges



        }

        // Legacy detection system removed - now handled by VisionSystem

        /// <summary>
        /// Check if a unit is within field of view
        /// </summary>
        private bool IsInFieldOfView(Unit target)
        {
            return RTSUtilities.IsInFieldOfView(this, target, fieldOfViewAngle);
        }

        /// <summary>
        /// Check if there's a clear line of sight to target
        /// </summary>
        private bool HasLineOfSight(Unit target)
        {
            return RTSUtilities.HasLineOfSight(this, target, visionBlockingLayers);
        }

        /// <summary>
        /// Update combat execution - AI units controlled by AI, player units have autonomous targeting
        /// </summary>
        private void UpdateCombat()
        {
            if (!canAttack) return;

            // Check if current target is still valid
            if (currentTarget != null)
            {
                if (!IsValidTarget(currentTarget))
                {
                    // Clear invalid target
                    SetTarget(null);
                }
                else
                {
                    // Execute attack on current target
                    TryAttackTarget(currentTarget);
                    return; // Don't look for new targets while attacking current one
                }
            }

            // Autonomous target acquisition for player units only
            // AI units are controlled by AI controller
            AI aiComponent = GetComponent<AI>();
            bool isAIControlled = aiComponent != null;

            if (!isAIControlled && autoAcquireTargets && detectedEnemies.Count > 0)
            {
                // Player units get autonomous targeting
                Unit bestTarget = GetBestTarget();
                if (bestTarget != null)
                {
                    SetTarget(bestTarget);
                }
            }
            // AI-controlled units: targeting decisions made by AI controller only
        }

        /// <summary>
        /// Check if a target is valid for combat
        /// </summary>
        private bool IsValidTarget(Unit target)
        {
            if (target == null || target.CurrentHealth <= 0 || target.IsDead) return false;

            float distance = Vector3.Distance(transform.position, target.transform.position);
            if (distance > visionRange) return false; // Use vision range for target validation

            // Check faction relationship
            if (FactionManager.Instance != null)
            {
                return FactionManager.Instance.AreFactionsHostile(this.faction, target.faction);
            }

            return false;
        }

        /// <summary>
        /// Set a new combat target
        /// </summary>
        public virtual void SetTarget(Unit newTarget)
        {
            if (currentTarget == newTarget) return;

            Unit previousTarget = currentTarget;
            currentTarget = newTarget;

            if (previousTarget != null)
            {
                OnTargetLost?.Invoke(this);
            }

            if (currentTarget != null)
            {
                OnTargetAcquired?.Invoke(this, currentTarget);
            }

            isInCombat = currentTarget != null;
        }

        /// <summary>
        /// Attempt to attack the specified target
        /// </summary>
        private void TryAttackTarget(Unit target)
        {
            if (weaponSystem == null)
            {
                Debug.LogWarning($"[Combat] {gameObject.name}: No weapon system available");
                return;
            }

            if (!IsValidTarget(target))
            {
                Debug.Log($"[Combat] {gameObject.name}: Invalid target {(target != null ? target.name : "null")}");
                return;
            }

            // Use WeaponSystem to handle firing
            bool fired = weaponSystem.TryFire(target.transform.position, target);

            if (fired)
            {
                // Combat state is handled by the weapon system event handlers
                Debug.Log($"[Combat] {gameObject.name}: Successfully fired at {target.name}");
            }
        }

        /// <summary>
        /// Perform an attack on the target - now handled by WeaponSystem
        /// </summary>
        protected virtual void PerformAttack(Unit target)
        {
            // This method is now deprecated - attacks are handled by WeaponSystem
            // Keeping for compatibility but delegating to TryAttackTarget
            TryAttackTarget(target);
        }

        /// <summary>
        /// Get the closest enemy unit within detection range
        /// </summary>
        public Unit GetClosestEnemy()
        {
            return RTSUtilities.FindClosestUnit(transform.position, detectedEnemies);
        }

        /// <summary>
        /// Get the best tactical target using TacticalAwareness if available
        /// </summary>
        private Unit GetBestTarget()
        {
            if (detectedEnemies.Count == 0) return null;

            // Use tactical awareness for target selection if available
            if (TacticalAwareness.Instance != null)
            {
                var situation = TacticalAwareness.Instance.GetUnitSituation(this);
                if (situation != null && situation.immediateThreats.Count > 0)
                {
                    // Select highest threat target
                    var highestThreat = situation.immediateThreats
                        .OrderByDescending(t => t.threatScore)
                        .FirstOrDefault();

                    if (highestThreat != null && detectedEnemies.Contains(highestThreat.threatUnit))
                    {
                        return highestThreat.threatUnit;
                    }
                }
            }

            // Fallback to closest enemy
            return GetClosestEnemy();
        }

        /// <summary>
        /// Attack a specific target (public method for AI/player commands)
        /// </summary>
        public virtual bool AttackTarget(Unit target)
        {
            if (!canAttack || target == null)
            {
                // AttackTarget failed
                return false;
            }

            if (FactionManager.Instance != null &&
                !FactionManager.Instance.AreFactionsHostile(this.faction, target.faction))
            {
                // Factions not hostile
                return false; // Can't attack non-hostile units
            }

            if (currentTarget != target)
            {
                DebugManager.Instance?.LogCombat($"{gameObject.name}: Target {(target ? target.name : "null")}", "targeting");
            }
            SetTarget(target);
            return true;
        }

        #endregion

        #region Interface Implementations

        // ITargetable implementation
        Vector3 ITargetable.Position => transform.position;
        Vector3 ITargetable.TargetPoint => transform.position + Vector3.up * 1f; // Aim at center mass
        bool ITargetable.IsValidTarget => !isDead && gameObject.activeInHierarchy;
        bool ITargetable.IsAlive => !isDead;
        float ITargetable.GetTargetRadius() => unitCollider?.bounds.size.magnitude ?? 1f;

        // IWeaponPlatform implementation
        Vector3 IWeaponPlatform.FirePosition => transform.position + Vector3.up * 1.5f; // Elevated fire position
        Vector3 IWeaponPlatform.Forward => transform.forward;
        bool IWeaponPlatform.CanEngageTarget(ITargetable target)
        {
            if (target == null || !target.IsValidTarget) return false;

            float distance = Vector3.Distance(transform.position, target.Position);
            return distance <= WeaponRange && CanAttack;
        }
        Unit IWeaponPlatform.Unit => this;
        bool IWeaponPlatform.IsMoving => IsMoving;

        #endregion


    }

    /// <summary>
    /// Enum defining different types of units
    /// </summary>
    public enum UnitType
    {
        Infantry,
        LightVehicle,
        HeavyVehicle,
        Tank,
        Artillery,
        Aircraft,
        Building
    }

    /// <summary>
    /// Enum defining different factions in the game
    /// </summary>
    public enum Faction
    {
        Player,
        Enemy,
        Neutral,
        Ally
    }

    /// <summary>
    /// Enum defining relationships between factions
    /// </summary>
    public enum FactionRelationship
    {
        Friendly,
        Neutral,
        Hostile
    }
}
