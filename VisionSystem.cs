using UnityEngine;
using System;
using System.Collections.Generic;

namespace ColdVor.RTS
{
    /// <summary>
    /// Dedicated vision system component for visual detection and target acquisition.
    /// Completely separate from weapon systems - handles only visual detection.
    /// </summary>
    public class VisionSystem : MonoBehaviour
    {
        [Header("Vision Configuration")]
        [SerializeField] private float visionRange = 30f;
        [SerializeField] private float fieldOfViewAngle = 120f;
        [SerializeField] private LayerMask detectionLayerMask = -1; // All layers (units can be on any layer)
        [SerializeField] private LayerMask visionBlockingLayers = 1; // Default layer (terrain)
        
        [Header("Detection Settings")]
        [SerializeField] private float detectionUpdateInterval = 1f; // Further reduced frequency to prevent stuttering
        [SerializeField] private bool useHeightAdvantage = true;
        [SerializeField] private float heightAdvantageMultiplier = 1.5f;
        [SerializeField] private float heightAdvantageThreshold = 5f;
        
        [Header("Visual Feedback")]
        [SerializeField] private bool showVisionRange = false;
        [SerializeField] private bool showFieldOfView = false;
        [SerializeField] private Color visionRangeColor = Color.green;
        [SerializeField] private Color fieldOfViewColor = Color.yellow;
        
        // Detection state
        private List<Unit> visibleEnemies = new List<Unit>();
        private List<Unit> visibleFriendlies = new List<Unit>();
        private List<Unit> allVisibleUnits = new List<Unit>();
        private float lastDetectionUpdate;
        
        // Cached components
        private Unit ownerUnit;
        private FactionManager factionManager;
        
        // Events
        public event Action<VisionSystem, Unit> OnEnemyDetected;
        public event Action<VisionSystem, Unit> OnEnemyLost;
        public event Action<VisionSystem, Unit> OnFriendlyDetected;
        public event Action<VisionSystem, Unit> OnFriendlyLost;
        
        // Properties
        public float VisionRange => GetEffectiveVisionRange();
        public float FieldOfView => fieldOfViewAngle;
        public IReadOnlyList<Unit> VisibleEnemies => visibleEnemies;
        public IReadOnlyList<Unit> VisibleFriendlies => visibleFriendlies;
        public IReadOnlyList<Unit> AllVisibleUnits => allVisibleUnits;
        public Vector3 EyePosition => transform.position + Vector3.up * 1.5f;
        
        private void Awake()
        {
            ownerUnit = GetComponent<Unit>();
            // Don't cache FactionManager in Awake - get it when needed
        }
        
        private void Update()
        {
            // Debug to see if Update is being called
            if (DebugManager.Instance != null && Time.frameCount % 60 == 0) // Every 60 frames
            {
                DebugManager.Instance.LogSystem("VisionSystem", $"{ownerUnit?.name ?? "Unknown"}: VisionSystem Update called");
            }

            UpdateDetection();
        }
        
        /// <summary>
        /// Update unit detection based on vision parameters
        /// </summary>
        private void UpdateDetection()
        {
            // Limit detection updates for performance
            if (Time.time - lastDetectionUpdate < detectionUpdateInterval)
                return;

            lastDetectionUpdate = Time.time;

            // Get FactionManager when needed (not cached in Awake)
            var factionManager = FactionManager.Instance;

            if (ownerUnit == null || ownerUnit.IsDead || factionManager == null)
            {
                if (DebugManager.Instance != null)
                {
                    DebugManager.Instance.LogSystem("VisionSystem", $"VisionSystem: Skipping detection - ownerUnit={ownerUnit?.name}, isDead={ownerUnit?.IsDead}, factionManager={factionManager != null}");
                }
                return;
            }

            // Debug logging
            if (DebugManager.Instance != null)
            {
                DebugManager.Instance.LogSystem("VisionSystem", $"{ownerUnit.name}: Starting detection scan - Faction: {ownerUnit.Faction}, Range: {VisionRange}");
            }
                
            // Store previous detections for comparison
            var previousEnemies = new List<Unit>(visibleEnemies);
            var previousFriendlies = new List<Unit>(visibleFriendlies);
            
            // Clear current detections
            visibleEnemies.Clear();
            visibleFriendlies.Clear();
            allVisibleUnits.Clear();
            
            // Find all units in vision range - TEMPORARILY IGNORE LAYER MASK FOR DEBUG
            Collider[] nearbyColliders = Physics.OverlapSphere(transform.position, VisionRange);

            // Debug logging
            if (DebugManager.Instance != null)
            {
                DebugManager.Instance.LogSystem("VisionSystem", $"{ownerUnit.name}: Found {nearbyColliders.Length} colliders in range {VisionRange}");
            }

            // Debug logging
            if (DebugManager.Instance != null)
            {
                DebugManager.Instance.LogSystem("VisionSystem", $"{ownerUnit.name}: Found {nearbyColliders.Length} colliders in range {VisionRange}");
            }

            foreach (Collider collider in nearbyColliders)
            {
                Unit detectedUnit = collider.GetComponent<Unit>();
                if (detectedUnit == null || detectedUnit == ownerUnit || detectedUnit.IsDead)
                    continue;

                // Debug logging
                if (DebugManager.Instance != null)
                {
                    DebugManager.Instance.LogSystem("VisionSystem", $"{ownerUnit.name}: Checking unit {detectedUnit.name} (Faction: {detectedUnit.Faction})");
                }

                bool canSee = CanSee(detectedUnit);

                // Debug logging
                if (DebugManager.Instance != null)
                {
                    DebugManager.Instance.LogSystem("VisionSystem", $"{ownerUnit.name}: CanSee({detectedUnit.name}) = {canSee}");
                }

                if (canSee)
                {
                    allVisibleUnits.Add(detectedUnit);

                    // Categorize by faction relationship
                    FactionRelationship relationship = factionManager.GetFactionRelationship(
                        ownerUnit.Faction, detectedUnit.Faction);

                    // Debug logging
                    if (DebugManager.Instance != null)
                    {
                        DebugManager.Instance.LogSystem("VisionSystem", $"{ownerUnit.name}: {ownerUnit.Faction} vs {detectedUnit.Faction} = {relationship}");
                    }

                    if (relationship == FactionRelationship.Hostile)
                    {
                        visibleEnemies.Add(detectedUnit);

                        // Trigger enemy detected event for new detections
                        if (!previousEnemies.Contains(detectedUnit))
                        {
                            OnEnemyDetected?.Invoke(this, detectedUnit);

                            // Debug logging
                            if (DebugManager.Instance != null)
                            {
                                DebugManager.Instance.LogSystem("VisionSystem", $"{ownerUnit.name}: ENEMY DETECTED - {detectedUnit.name}!");
                            }
                        }
                    }
                    else if (relationship == FactionRelationship.Friendly)
                    {
                        visibleFriendlies.Add(detectedUnit);
                        
                        // Trigger friendly detected event for new detections
                        if (!previousFriendlies.Contains(detectedUnit))
                        {
                            OnFriendlyDetected?.Invoke(this, detectedUnit);
                        }
                    }
                }
            }
            
            // Trigger lost events for units no longer visible
            foreach (Unit previousEnemy in previousEnemies)
            {
                if (!visibleEnemies.Contains(previousEnemy))
                {
                    OnEnemyLost?.Invoke(this, previousEnemy);
                }
            }
            
            foreach (Unit previousFriendly in previousFriendlies)
            {
                if (!visibleFriendlies.Contains(previousFriendly))
                {
                    OnFriendlyLost?.Invoke(this, previousFriendly);
                }
            }
        }
        
        /// <summary>
        /// Check if this unit can see the target unit
        /// </summary>
        public bool CanSee(Unit target)
        {
            if (target == null || target.IsDead)
                return false;
                
            Vector3 targetPosition = target.transform.position + Vector3.up * 1f;
            return CanSee(targetPosition);
        }
        
        /// <summary>
        /// Check if this unit can see a specific position
        /// </summary>
        public bool CanSee(Vector3 targetPosition)
        {
            Vector3 eyePos = EyePosition;
            
            // Check distance
            float distance = Vector3.Distance(eyePos, targetPosition);
            if (distance > VisionRange)
                return false;
                
            // Check field of view
            Vector3 directionToTarget = (targetPosition - eyePos).normalized;
            Vector3 forward = transform.forward;
            float angle = Vector3.Angle(forward, directionToTarget);
            
            if (angle > fieldOfViewAngle * 0.5f)
                return false;
                
            // Check line of sight
            return HasLineOfSight(eyePos, targetPosition);
        }
        
        /// <summary>
        /// Check if there's a clear line of sight between two positions
        /// </summary>
        public bool HasLineOfSight(Vector3 from, Vector3 to)
        {
            Vector3 direction = (to - from).normalized;
            float distance = Vector3.Distance(from, to);
            
            // Raycast to check for obstructions
            return !Physics.Raycast(from, direction, distance, visionBlockingLayers);
        }
        
        /// <summary>
        /// Get the nearest visible enemy
        /// </summary>
        public Unit GetNearestEnemy()
        {
            if (visibleEnemies.Count == 0)
                return null;
                
            Unit nearest = null;
            float nearestDistance = float.MaxValue;
            
            foreach (Unit enemy in visibleEnemies)
            {
                float distance = Vector3.Distance(transform.position, enemy.transform.position);
                if (distance < nearestDistance)
                {
                    nearestDistance = distance;
                    nearest = enemy;
                }
            }
            
            return nearest;
        }
        
        /// <summary>
        /// Get the nearest visible friendly
        /// </summary>
        public Unit GetNearestFriendly()
        {
            if (visibleFriendlies.Count == 0)
                return null;
                
            Unit nearest = null;
            float nearestDistance = float.MaxValue;
            
            foreach (Unit friendly in visibleFriendlies)
            {
                float distance = Vector3.Distance(transform.position, friendly.transform.position);
                if (distance < nearestDistance)
                {
                    nearestDistance = distance;
                    nearest = friendly;
                }
            }
            
            return nearest;
        }
        
        /// <summary>
        /// Get effective vision range including height advantage
        /// </summary>
        private float GetEffectiveVisionRange()
        {
            float effectiveRange = visionRange;
            
            if (useHeightAdvantage)
            {
                float height = transform.position.y;
                if (height > heightAdvantageThreshold)
                {
                    effectiveRange *= heightAdvantageMultiplier;
                }
            }
            
            return effectiveRange;
        }
        
        /// <summary>
        /// Set vision range (for dynamic vision changes)
        /// </summary>
        public void SetVisionRange(float newRange)
        {
            visionRange = Mathf.Max(0f, newRange);
        }
        
        /// <summary>
        /// Set field of view angle
        /// </summary>
        public void SetFieldOfView(float newAngle)
        {
            fieldOfViewAngle = Mathf.Clamp(newAngle, 0f, 360f);
        }
        
        /// <summary>
        /// Check if a position is within vision range
        /// </summary>
        public bool IsInVisionRange(Vector3 position)
        {
            return Vector3.Distance(transform.position, position) <= VisionRange;
        }
        
        /// <summary>
        /// Check if a position is within field of view
        /// </summary>
        public bool IsInFieldOfView(Vector3 position)
        {
            Vector3 directionToTarget = (position - EyePosition).normalized;
            Vector3 forward = transform.forward;
            float angle = Vector3.Angle(forward, directionToTarget);
            
            return angle <= fieldOfViewAngle * 0.5f;
        }
        
        private void OnDrawGizmosSelected()
        {
            if (!showVisionRange && !showFieldOfView)
                return;
                
            Vector3 position = transform.position;
            
            // Draw vision range
            if (showVisionRange)
            {
                Gizmos.color = visionRangeColor;
                Gizmos.DrawWireSphere(position, VisionRange);
            }
            
            // Draw field of view
            if (showFieldOfView)
            {
                Gizmos.color = fieldOfViewColor;
                Vector3 forward = transform.forward;
                float halfAngle = fieldOfViewAngle * 0.5f;
                
                Vector3 leftBoundary = Quaternion.AngleAxis(-halfAngle, Vector3.up) * forward * VisionRange;
                Vector3 rightBoundary = Quaternion.AngleAxis(halfAngle, Vector3.up) * forward * VisionRange;
                
                Gizmos.DrawLine(position, position + leftBoundary);
                Gizmos.DrawLine(position, position + rightBoundary);
                
                // Draw arc
                Vector3 previousPoint = position + leftBoundary;
                for (int i = 1; i <= 20; i++)
                {
                    float angle = Mathf.Lerp(-halfAngle, halfAngle, i / 20f);
                    Vector3 point = position + Quaternion.AngleAxis(angle, Vector3.up) * forward * VisionRange;
                    Gizmos.DrawLine(previousPoint, point);
                    previousPoint = point;
                }
            }
        }
    }
}
