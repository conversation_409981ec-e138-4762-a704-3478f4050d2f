using UnityEngine;

namespace ColdVor.RTS
{
    /// <summary>
    /// Interface for objects that can be selected in the RTS system
    /// </summary>
    public interface ISelectable
    {
        /// <summary>
        /// Whether this object is currently selected
        /// </summary>
        bool IsSelected { get; }

        /// <summary>
        /// The GameObject this selectable is attached to
        /// </summary>
        GameObject GameObject { get; }

        /// <summary>
        /// The transform of this selectable object
        /// </summary>
        Transform Transform { get; }

        /// <summary>
        /// Called when this object is selected
        /// </summary>
        void OnSelected();

        /// <summary>
        /// Called when this object is deselected
        /// </summary>
        void OnDeselected();

        /// <summary>
        /// Get the bounds of this selectable object for selection calculations
        /// </summary>
        Bounds GetSelectionBounds();

        /// <summary>
        /// Check if this object can be selected
        /// </summary>
        bool CanBeSelected();
    }
}
