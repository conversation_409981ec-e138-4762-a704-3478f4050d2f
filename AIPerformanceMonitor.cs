using UnityEngine;
using Unity.Profiling;

namespace ColdVor.RTS
{
    /// <summary>
    /// Performance monitoring for AI systems
    /// Tracks job system performance and provides optimization insights
    /// </summary>
    public class AIPerformanceMonitor : MonoBehaviour
    {
        [Header("Performance Monitoring")]
        [SerializeField] private bool enableProfiling = true;
        [SerializeField] private bool showDebugUI = false;
        [SerializeField] private float updateInterval = 1f;

        // Performance metrics
        private ProfilerRecorder aiJobRecorder;
        private ProfilerRecorder mainThreadRecorder;
        private ProfilerRecorder memoryRecorder;
        
        // Statistics
        private float lastUpdateTime;
        private int totalUnitsProcessed;
        private float averageJobTime;
        private float peakJobTime;
        private int framesSinceLastUpdate;

        private void Start()
        {
            if (enableProfiling)
            {
                // Initialize profiler recorders
                aiJobRecorder = ProfilerRecorder.StartNew(ProfilerCategory.Scripts, "AI Job System");
                mainThreadRecorder = ProfilerRecorder.StartNew(ProfilerCategory.Scripts, "Main Thread");
                memoryRecorder = ProfilerRecorder.StartNew(ProfilerCategory.Memory, "Total Used Memory");
            }
        }

        private void Update()
        {
            if (!enableProfiling) return;

            framesSinceLastUpdate++;

            if (Time.time - lastUpdateTime >= updateInterval)
            {
                UpdatePerformanceMetrics();
                lastUpdateTime = Time.time;
                framesSinceLastUpdate = 0;
            }
        }

        private void UpdatePerformanceMetrics()
        {
            if (AIJobSystem.Instance != null)
            {
                // Get current unit count
                totalUnitsProcessed = AIJobSystem.Instance.RegisteredUnitCount;
                
                // Calculate performance metrics
                if (aiJobRecorder.Valid)
                {
                    // Use LastValue instead of GetSamples for compatibility
                    long lastValue = aiJobRecorder.LastValue;
                    if (lastValue > 0)
                    {
                        float sampleTime = lastValue * 1e-6f; // Convert to milliseconds
                        averageJobTime = sampleTime;
                        peakJobTime = Mathf.Max(peakJobTime, sampleTime);
                    }
                }

                // Log performance warnings
                CheckPerformanceThresholds();
            }
        }

        private void CheckPerformanceThresholds()
        {
            // Warn if AI jobs are taking too long
            if (averageJobTime > 5f) // 5ms threshold
            {
                Debug.LogWarning($"AI Job System: High processing time detected ({averageJobTime:F2}ms avg, {totalUnitsProcessed} units)");
            }

            // Warn if too many units for current settings
            if (totalUnitsProcessed > 500 && averageJobTime > 3f)
            {
                Debug.LogWarning($"AI Job System: Consider increasing batch size or reducing update frequency for {totalUnitsProcessed} units");
            }
        }

        private void OnGUI()
        {
            if (!showDebugUI) return;

            GUILayout.BeginArea(new Rect(10, 10, 300, 200));
            GUILayout.BeginVertical("box");
            
            GUILayout.Label("AI Performance Monitor", GUI.skin.label);
            GUILayout.Space(5);
            
            GUILayout.Label($"Units Processed: {totalUnitsProcessed}");
            GUILayout.Label($"Average Job Time: {averageJobTime:F2}ms");
            GUILayout.Label($"Peak Job Time: {peakJobTime:F2}ms");
            GUILayout.Label($"FPS: {1f / Time.unscaledDeltaTime:F1}");
            
            if (memoryRecorder.Valid)
            {
                long memoryUsage = memoryRecorder.LastValue;
                GUILayout.Label($"Memory: {memoryUsage / (1024 * 1024)}MB");
            }
            
            GUILayout.Space(10);
            
            // Performance recommendations
            if (totalUnitsProcessed > 1000)
            {
                GUILayout.Label("Recommendations:", GUI.skin.label);
                if (averageJobTime > 3f)
                {
                    GUILayout.Label("• Increase batch size");
                    GUILayout.Label("• Reduce AI update frequency");
                }
                if (totalUnitsProcessed > 2000)
                {
                    GUILayout.Label("• Consider LOD system");
                    GUILayout.Label("• Implement spatial partitioning");
                }
            }
            
            GUILayout.EndVertical();
            GUILayout.EndArea();
        }

        private void OnDestroy()
        {
            // Clean up profiler recorders
            if (aiJobRecorder.Valid)
                aiJobRecorder.Dispose();
            if (mainThreadRecorder.Valid)
                mainThreadRecorder.Dispose();
            if (memoryRecorder.Valid)
                memoryRecorder.Dispose();
        }

        // Public API for external monitoring
        public float GetAverageJobTime() => averageJobTime;
        public float GetPeakJobTime() => peakJobTime;
        public int GetTotalUnitsProcessed() => totalUnitsProcessed;
        
        public void ResetPeakTime()
        {
            peakJobTime = 0f;
        }
    }
}
