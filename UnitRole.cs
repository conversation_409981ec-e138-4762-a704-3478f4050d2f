using UnityEngine;
using System.Collections.Generic;

namespace ColdVor.RTS
{
    /// <summary>
    /// Defines the tactical role and doctrine for a unit
    /// This determines how the unit behaves in combined arms operations
    /// </summary>
    [CreateAssetMenu(fileName = "New Unit Role", menuName = "ColdVor/Unit Role")]
    public class UnitRole : ScriptableObject
    {
        [Header("Role Definition")]
        [SerializeField] private string roleName = "Infantry";
        [SerializeField] private UnitRoleType roleType = UnitRoleType.Assault;
        [SerializeField] private TacticalDoctrine doctrine = TacticalDoctrine.Aggressive;
        [SerializeField] private string description = "";

        [Header("Combat Behavior")]
        [SerializeField] private float preferredEngagementRange = 15f;
        [SerializeField] private float minimumEngagementRange = 5f;
        [SerializeField] private float maxEngagementRange = 30f;
        [SerializeField] private bool canProvideFireSupport = false;
        [SerializeField] private bool requiresSupport = false;

        [Header("Movement Behavior")]
        [SerializeField] private float movementSpeed = 1f; // Multiplier for base speed
        [SerializeField] private bool canAdvanceAlone = true;
        [SerializeField] private bool prefersFormations = false;
        [SerializeField] private FormationType preferredFormation = FormationType.Line;

        [Header("Tactical Priorities")]
        [SerializeField] private List<UnitRoleType> supportedRoles = new List<UnitRoleType>();
        [SerializeField] private List<UnitRoleType> supportingRoles = new List<UnitRoleType>();
        [SerializeField] private float coordinationRadius = 25f;

        [Header("Special Abilities")]
        [SerializeField] private bool canScout = false;
        [SerializeField] private bool canCallArtillery = false;
        [SerializeField] private bool canRepair = false;
        [SerializeField] private bool canResupply = false;

        // Public Properties
        public string RoleName => roleName;
        public UnitRoleType RoleType => roleType;
        public TacticalDoctrine Doctrine => doctrine;
        public string Description => description;
        public float PreferredEngagementRange => preferredEngagementRange;
        public float MinimumEngagementRange => minimumEngagementRange;
        public float MaxEngagementRange => maxEngagementRange;
        public bool CanProvideFireSupport => canProvideFireSupport;
        public bool RequiresSupport => requiresSupport;
        public float MovementSpeed => movementSpeed;
        public bool CanAdvanceAlone => canAdvanceAlone;
        public bool PrefersFormations => prefersFormations;
        public FormationType PreferredFormation => preferredFormation;
        public List<UnitRoleType> SupportedRoles => new List<UnitRoleType>(supportedRoles);
        public List<UnitRoleType> SupportingRoles => new List<UnitRoleType>(supportingRoles);
        public float CoordinationRadius => coordinationRadius;
        public bool CanScout => canScout;
        public bool CanCallArtillery => canCallArtillery;
        public bool CanRepair => canRepair;
        public bool CanResupply => canResupply;

        /// <summary>
        /// Get tactical behavior parameters for this role
        /// </summary>
        public TacticalBehavior GetTacticalBehavior()
        {
            return new TacticalBehavior
            {
                aggressiveness = GetAggressiveness(),
                caution = GetCaution(),
                supportOriented = RequiresSupport || CanProvideFireSupport,
                independentOperations = CanAdvanceAlone,
                formationDiscipline = PrefersFormations ? 0.8f : 0.3f
            };
        }

        private float GetAggressiveness()
        {
            return doctrine switch
            {
                TacticalDoctrine.Aggressive => 0.8f,
                TacticalDoctrine.Balanced => 0.5f,
                TacticalDoctrine.Defensive => 0.2f,
                TacticalDoctrine.Support => 0.3f,
                _ => 0.5f
            };
        }

        private float GetCaution()
        {
            return doctrine switch
            {
                TacticalDoctrine.Aggressive => 0.2f,
                TacticalDoctrine.Balanced => 0.5f,
                TacticalDoctrine.Defensive => 0.8f,
                TacticalDoctrine.Support => 0.6f,
                _ => 0.5f
            };
        }
    }

    /// <summary>
    /// Types of tactical roles units can have
    /// </summary>
    public enum UnitRoleType
    {
        Assault,        // Front-line combat units
        Support,        // Fire support and heavy weapons
        Recon,          // Scouting and intelligence
        Artillery,      // Long-range fire support
        AntiAir,        // Air defense
        Engineer,       // Construction and repair
        Logistics,      // Supply and transport
        Command,        // Command and control
        Medic,          // Medical support
        Specialist      // Special operations
    }

    /// <summary>
    /// Tactical doctrines that guide unit behavior
    /// </summary>
    public enum TacticalDoctrine
    {
        Aggressive,     // Advance and attack aggressively
        Balanced,       // Balanced approach to offense/defense
        Defensive,      // Prioritize defense and cover
        Support         // Focus on supporting other units
    }

    // FormationType enum moved to PathfindingManager.cs to avoid duplication

    /// <summary>
    /// Tactical behavior parameters
    /// </summary>
    [System.Serializable]
    public class TacticalBehavior
    {
        public float aggressiveness;      // 0-1, how aggressive the unit is
        public float caution;             // 0-1, how cautious the unit is
        public bool supportOriented;     // Whether unit focuses on supporting others
        public bool independentOperations; // Can operate alone effectively
        public float formationDiscipline; // 0-1, how well unit maintains formation
    }
}
