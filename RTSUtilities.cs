using UnityEngine;

namespace ColdVor.RTS
{
    /// <summary>
    /// Utility class for common RTS calculations and operations
    /// Consolidates redundant methods from across the codebase
    /// </summary>
    public static class RTSUtilities
    {
        #region Distance and Position Calculations

        /// <summary>
        /// Calculate distance between two positions
        /// </summary>
        public static float GetDistance(Vector3 pos1, Vector3 pos2)
        {
            return Vector3.Distance(pos1, pos2);
        }

        /// <summary>
        /// Calculate distance between two units
        /// </summary>
        public static float GetDistance(Unit unit1, Unit unit2)
        {
            if (unit1 == null || unit2 == null) return float.MaxValue;
            return Vector3.Distance(unit1.transform.position, unit2.transform.position);
        }

        /// <summary>
        /// Calculate distance from unit to position
        /// </summary>
        public static float GetDistance(Unit unit, Vector3 position)
        {
            if (unit == null) return float.MaxValue;
            return Vector3.Distance(unit.transform.position, position);
        }

        /// <summary>
        /// Get ground position using raycast
        /// </summary>
        public static Vector3 GetGroundPosition(Vector3 position, LayerMask groundMask = default)
        {
            // If no mask specified, use all layers
            LayerMask mask = groundMask.value == 0 ? ~0 : groundMask;

            if (Physics.Raycast(position + Vector3.up * 10f, Vector3.down, out RaycastHit hit, 20f, mask))
            {
                return hit.point;
            }
            return position;
        }

        #endregion

        #region Vision and Line of Sight

        /// <summary>
        /// Check if there's a clear line of sight between two positions
        /// </summary>
        public static bool HasLineOfSight(Vector3 from, Vector3 to, LayerMask blockingLayers = default)
        {
            Vector3 direction = (to - from).normalized;
            float distance = Vector3.Distance(from, to);

            // If no mask specified, use all layers
            LayerMask mask = blockingLayers.value == 0 ? ~0 : blockingLayers;

            return !Physics.Raycast(from, direction, distance, mask);
        }

        /// <summary>
        /// Check if there's a clear line of sight between two units
        /// </summary>
        public static bool HasLineOfSight(Unit from, Unit to, LayerMask blockingLayers = default)
        {
            if (from == null || to == null) return false;

            Vector3 fromPos = from.transform.position + Vector3.up * 1.5f; // Eye level
            Vector3 toPos = to.transform.position + Vector3.up * 1.5f;

            return HasLineOfSight(fromPos, toPos, blockingLayers);
        }

        /// <summary>
        /// Check if target is within field of view
        /// </summary>
        public static bool IsInFieldOfView(Unit observer, Unit target, float fovAngle)
        {
            if (observer == null || target == null) return false;

            Vector3 directionToTarget = (target.transform.position - observer.transform.position).normalized;
            float angle = Vector3.Angle(observer.transform.forward, directionToTarget);
            return angle <= fovAngle * 0.5f;
        }

        /// <summary>
        /// Check if target is within field of view from position and direction
        /// </summary>
        public static bool IsInFieldOfView(Vector3 observerPos, Vector3 observerForward, Vector3 targetPos, float fovAngle)
        {
            Vector3 directionToTarget = (targetPos - observerPos).normalized;
            float angle = Vector3.Angle(observerForward, directionToTarget);
            return angle <= fovAngle * 0.5f;
        }

        #endregion

        #region Combat Calculations

        /// <summary>
        /// Calculate damage based on distance (for explosions, etc.)
        /// </summary>
        public static int CalculateDistanceBasedDamage(int baseDamage, float distance, float maxRange)
        {
            if (distance >= maxRange) return 0;
            
            float damageMultiplier = 1f - (distance / maxRange);
            return Mathf.RoundToInt(baseDamage * damageMultiplier);
        }

        /// <summary>
        /// Calculate optimal attack position maintaining weapon range
        /// </summary>
        public static Vector3 CalculateOptimalAttackPosition(Unit attacker, Unit target, float optimalRange)
        {
            if (attacker == null || target == null) return attacker.transform.position;

            Vector3 directionToTarget = (target.transform.position - attacker.transform.position).normalized;
            return target.transform.position - directionToTarget * optimalRange;
        }

        #endregion

        #region Unit Finding and Filtering

        /// <summary>
        /// Find closest unit from a list
        /// </summary>
        public static Unit FindClosestUnit(Vector3 position, System.Collections.Generic.List<Unit> units)
        {
            Unit closest = null;
            float closestDistance = float.MaxValue;

            foreach (Unit unit in units)
            {
                if (unit == null) continue;

                float distance = GetDistance(position, unit.transform.position);
                if (distance < closestDistance)
                {
                    closestDistance = distance;
                    closest = unit;
                }
            }

            return closest;
        }

        /// <summary>
        /// Find units within range of a position
        /// </summary>
        public static System.Collections.Generic.List<Unit> FindUnitsInRange(Vector3 position, float range, LayerMask layerMask = default)
        {
            var unitsInRange = new System.Collections.Generic.List<Unit>();

            // If no mask specified, use all layers
            LayerMask mask = layerMask.value == 0 ? ~0 : layerMask;

            Collider[] colliders = Physics.OverlapSphere(position, range, mask);

            foreach (Collider col in colliders)
            {
                Unit unit = col.GetComponent<Unit>();
                if (unit != null)
                {
                    unitsInRange.Add(unit);
                }
            }

            return unitsInRange;
        }

        /// <summary>
        /// Find closest enemy unit within range
        /// </summary>
        public static Unit FindClosestEnemyInRange(Unit observer, float range)
        {
            if (observer == null) return null;

            var unitsInRange = FindUnitsInRange(observer.transform.position, range);
            var enemies = new System.Collections.Generic.List<Unit>();

            foreach (Unit unit in unitsInRange)
            {
                if (unit != observer && FactionManager.Instance != null &&
                    FactionManager.Instance.AreFactionsHostile(observer.Faction, unit.Faction))
                {
                    enemies.Add(unit);
                }
            }

            return FindClosestUnit(observer.transform.position, enemies);
        }

        /// <summary>
        /// Find allies within range of a unit
        /// </summary>
        public static System.Collections.Generic.List<Unit> FindAlliesInRange(Unit observer, float range)
        {
            if (observer == null) return new System.Collections.Generic.List<Unit>();

            var unitsInRange = FindUnitsInRange(observer.transform.position, range);
            var allies = new System.Collections.Generic.List<Unit>();

            foreach (Unit unit in unitsInRange)
            {
                if (unit != observer && FactionManager.Instance != null &&
                    FactionManager.Instance.AreFactionsFriendly(observer.Faction, unit.Faction))
                {
                    allies.Add(unit);
                }
            }

            return allies;
        }

        #endregion

        #region Health and Combat Utilities

        /// <summary>
        /// Calculate health percentage
        /// </summary>
        public static float GetHealthPercentage(Unit unit)
        {
            if (unit == null) return 0f;
            return (float)unit.CurrentHealth / unit.MaxHealth;
        }

        /// <summary>
        /// Calculate combat power of a unit
        /// </summary>
        public static float CalculateCombatPower(Unit unit)
        {
            if (unit == null) return 0f;

            float healthFactor = GetHealthPercentage(unit);
            float weaponPower = unit.GetWeaponRange() * 0.1f + 1f;

            return healthFactor * weaponPower;
        }

        #endregion

        #region Faction Utilities

        /// <summary>
        /// Check if two units are hostile to each other
        /// </summary>
        public static bool AreUnitsHostile(Unit unit1, Unit unit2)
        {
            if (unit1 == null || unit2 == null || FactionManager.Instance == null) return false;
            return FactionManager.Instance.AreFactionsHostile(unit1.Faction, unit2.Faction);
        }

        /// <summary>
        /// Check if two units are friendly to each other
        /// </summary>
        public static bool AreUnitsFriendly(Unit unit1, Unit unit2)
        {
            if (unit1 == null || unit2 == null || FactionManager.Instance == null) return false;
            return FactionManager.Instance.AreFactionsFriendly(unit1.Faction, unit2.Faction);
        }

        #endregion

        #region Collision and Layer Utilities

        /// <summary>
        /// Check if a layer is in a layer mask
        /// </summary>
        public static bool IsLayerInMask(int layer, LayerMask layerMask)
        {
            return (layerMask.value & (1 << layer)) != 0;
        }

        /// <summary>
        /// Get Unit component from collider, with null safety
        /// </summary>
        public static Unit GetUnitFromCollider(Collider collider)
        {
            return collider?.GetComponent<Unit>();
        }

        #endregion

        #region Material and Visual Utilities

        /// <summary>
        /// Create a material with specified shader and color
        /// </summary>
        public static Material CreateMaterial(string shaderName, Color color)
        {
            Material material = new Material(Shader.Find(shaderName));
            material.color = color;
            return material;
        }

        /// <summary>
        /// Create a glowing material with emission
        /// </summary>
        public static Material CreateGlowingMaterial(string shaderName, Color color, float emissionIntensity = 2f)
        {
            Material material = CreateMaterial(shaderName, color);
            material.EnableKeyword("_EMISSION");
            material.SetColor("_EmissionColor", color * emissionIntensity);
            return material;
        }

        #endregion

        #region Unit Collection Management

        /// <summary>
        /// Refresh a unit list with all active units
        /// </summary>
        public static void RefreshUnitList(System.Collections.Generic.List<Unit> unitList)
        {
            unitList.Clear();
            Unit[] units = Object.FindObjectsByType<Unit>(FindObjectsSortMode.None);
            unitList.AddRange(units);
        }

        /// <summary>
        /// Get all units of a specific faction
        /// </summary>
        public static System.Collections.Generic.List<Unit> GetFactionUnits(Faction faction)
        {
            var factionUnits = new System.Collections.Generic.List<Unit>();
            Unit[] allUnits = Object.FindObjectsByType<Unit>(FindObjectsSortMode.None);

            foreach (Unit unit in allUnits)
            {
                if (unit != null && unit.Faction == faction)
                {
                    factionUnits.Add(unit);
                }
            }

            return factionUnits;
        }

        /// <summary>
        /// Clean up null references from a unit list
        /// </summary>
        public static void CleanupUnitList(System.Collections.Generic.List<Unit> unitList)
        {
            for (int i = unitList.Count - 1; i >= 0; i--)
            {
                if (unitList[i] == null || !unitList[i].gameObject.activeInHierarchy)
                {
                    unitList.RemoveAt(i);
                }
            }
        }

        #endregion

        #region Event Subscription Utilities

        /// <summary>
        /// Safely subscribe to an event (null check included)
        /// </summary>
        public static void SafeSubscribe<T>(ref System.Action<T> eventAction, System.Action<T> handler)
        {
            if (handler != null)
            {
                eventAction += handler;
            }
        }

        /// <summary>
        /// Safely unsubscribe from an event (null check included)
        /// </summary>
        public static void SafeUnsubscribe<T>(ref System.Action<T> eventAction, System.Action<T> handler)
        {
            if (handler != null)
            {
                eventAction -= handler;
            }
        }

        #endregion

        #region Cleanup Utilities

        /// <summary>
        /// Clean up old items from a list based on timestamp
        /// </summary>
        public static void CleanupOldItems<T>(System.Collections.Generic.List<T> items, System.Func<T, float> getTimestamp, float maxAge)
        {
            float currentTime = Time.time;
            for (int i = items.Count - 1; i >= 0; i--)
            {
                if (currentTime - getTimestamp(items[i]) > maxAge)
                {
                    items.RemoveAt(i);
                }
            }
        }

        #endregion

        #region Math Utilities

        /// <summary>
        /// Clamp angle to 0-360 range
        /// </summary>
        public static float ClampAngle(float angle)
        {
            while (angle < 0f) angle += 360f;
            while (angle >= 360f) angle -= 360f;
            return angle;
        }

        /// <summary>
        /// Calculate angle between two directions
        /// </summary>
        public static float GetAngleBetween(Vector3 from, Vector3 to)
        {
            return Vector3.Angle(from.normalized, to.normalized);
        }

        #endregion
    }
}
