using UnityEngine;
using ColdVor.RTS;

namespace ColdVor.AI.BehaviorTree
{
    /// <summary>
    /// Base class for all behavior tree nodes
    /// </summary>
    public abstract class BehaviorNode
    {
        public enum NodeState
        {
            Running,
            Success,
            Failure
        }

        protected Unit unit;
        protected BehaviorContext context;

        public BehaviorNode(Unit unit, BehaviorContext context)
        {
            this.unit = unit;
            this.context = context;
        }

        /// <summary>
        /// Execute this node and return its state
        /// </summary>
        public abstract NodeState Execute();

        /// <summary>
        /// Reset the node to its initial state
        /// </summary>
        public virtual void Reset() { }
    }

    /// <summary>
    /// Composite node that executes children in sequence until one fails
    /// </summary>
    public class SequenceNode : BehaviorNode
    {
        private BehaviorNode[] children;
        private int currentChild = 0;

        public SequenceNode(Unit unit, BehaviorContext context, params BehaviorNode[] children) 
            : base(unit, context)
        {
            this.children = children;
        }

        public override NodeState Execute()
        {
            while (currentChild < children.Length)
            {
                NodeState childState = children[currentChild].Execute();
                
                if (childState == NodeState.Failure)
                {
                    Reset();
                    return NodeState.Failure;
                }
                
                if (childState == NodeState.Running)
                {
                    return NodeState.Running;
                }
                
                // Child succeeded, move to next
                currentChild++;
            }
            
            // All children succeeded
            Reset();
            return NodeState.Success;
        }

        public override void Reset()
        {
            currentChild = 0;
            foreach (var child in children)
            {
                child.Reset();
            }
        }
    }

    /// <summary>
    /// Composite node that executes children until one succeeds
    /// </summary>
    public class SelectorNode : BehaviorNode
    {
        private BehaviorNode[] children;
        private int currentChild = 0;

        public SelectorNode(Unit unit, BehaviorContext context, params BehaviorNode[] children) 
            : base(unit, context)
        {
            this.children = children;
        }

        public override NodeState Execute()
        {
            while (currentChild < children.Length)
            {
                NodeState childState = children[currentChild].Execute();
                
                if (childState == NodeState.Success)
                {
                    Reset();
                    return NodeState.Success;
                }
                
                if (childState == NodeState.Running)
                {
                    return NodeState.Running;
                }
                
                // Child failed, try next
                currentChild++;
            }
            
            // All children failed
            Reset();
            return NodeState.Failure;
        }

        public override void Reset()
        {
            currentChild = 0;
            foreach (var child in children)
            {
                child.Reset();
            }
        }
    }

    /// <summary>
    /// Decorator that inverts the result of its child
    /// </summary>
    public class InverterNode : BehaviorNode
    {
        private BehaviorNode child;

        public InverterNode(Unit unit, BehaviorContext context, BehaviorNode child) 
            : base(unit, context)
        {
            this.child = child;
        }

        public override NodeState Execute()
        {
            NodeState childState = child.Execute();
            
            if (childState == NodeState.Success)
                return NodeState.Failure;
            if (childState == NodeState.Failure)
                return NodeState.Success;
                
            return NodeState.Running;
        }

        public override void Reset()
        {
            child.Reset();
        }
    }
}
