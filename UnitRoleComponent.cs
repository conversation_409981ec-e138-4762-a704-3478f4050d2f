using UnityEngine;

namespace ColdVor.RTS
{
    /// <summary>
    /// Component that defines a unit's tactical role and behavior
    /// Attached to units to specify their role in combined arms operations
    /// </summary>
    public class UnitRoleComponent : MonoBehaviour
    {
        [Header("Unit Role")]
        [SerializeField] private UnitRole role;
        [SerializeField] private bool overrideUnitTypeRole = false;

        [Header("Role Modifiers")]
        [SerializeField] private float experienceModifier = 1f;
        [SerializeField] private float moraleModifier = 1f;
        [SerializeField] private bool isVeteran = false;

        // Cached values
        private TacticalBehavior cachedBehavior;
        private bool behaviorCached = false;

        // Events
        public System.Action<UnitRole> OnRoleChanged;

        public UnitRole Role 
        { 
            get => role; 
            set 
            { 
                if (role != value)
                {
                    role = value;
                    behaviorCached = false;
                    OnRoleChanged?.Invoke(role);
                }
            } 
        }

        public float ExperienceModifier 
        { 
            get => experienceModifier; 
            set => experienceModifier = Mathf.Clamp01(value); 
        }

        public float MoraleModifier 
        { 
            get => moraleModifier; 
            set => moraleModifier = Mathf.Clamp01(value); 
        }

        public bool IsVeteran 
        { 
            get => isVeteran; 
            set => isVeteran = value; 
        }

        private void Start()
        {
            // Auto-assign role based on unit type if no role is set
            if (role == null && !overrideUnitTypeRole)
            {
                AssignDefaultRole();
            }
        }

        /// <summary>
        /// Get the tactical behavior for this unit, including modifiers
        /// </summary>
        public TacticalBehavior GetTacticalBehavior()
        {
            if (!behaviorCached || cachedBehavior == null)
            {
                cachedBehavior = CalculateTacticalBehavior();
                behaviorCached = true;
            }
            return cachedBehavior;
        }

        private TacticalBehavior CalculateTacticalBehavior()
        {
            if (role == null) return GetDefaultBehavior();

            var baseBehavior = role.GetTacticalBehavior();
            
            // Apply modifiers
            var modifiedBehavior = new TacticalBehavior
            {
                aggressiveness = baseBehavior.aggressiveness * GetAggressivenessModifier(),
                caution = baseBehavior.caution * GetCautionModifier(),
                supportOriented = baseBehavior.supportOriented,
                independentOperations = baseBehavior.independentOperations,
                formationDiscipline = baseBehavior.formationDiscipline * GetDisciplineModifier()
            };

            return modifiedBehavior;
        }

        private float GetAggressivenessModifier()
        {
            float modifier = 1f;
            
            // Experience increases aggressiveness
            modifier *= (0.8f + experienceModifier * 0.4f);
            
            // Morale affects aggressiveness
            modifier *= (0.6f + moraleModifier * 0.8f);
            
            // Veterans are more aggressive
            if (isVeteran) modifier *= 1.2f;
            
            return Mathf.Clamp(modifier, 0.1f, 2f);
        }

        private float GetCautionModifier()
        {
            float modifier = 1f;
            
            // Experience increases caution (tactical awareness)
            modifier *= (0.7f + experienceModifier * 0.6f);
            
            // Low morale increases caution
            modifier *= (1.5f - moraleModifier * 0.5f);
            
            // Veterans are more cautious (tactical)
            if (isVeteran) modifier *= 1.1f;
            
            return Mathf.Clamp(modifier, 0.1f, 2f);
        }

        private float GetDisciplineModifier()
        {
            float modifier = 1f;
            
            // Experience improves discipline
            modifier *= (0.6f + experienceModifier * 0.8f);
            
            // Morale affects discipline
            modifier *= (0.4f + moraleModifier * 1.2f);
            
            // Veterans have better discipline
            if (isVeteran) modifier *= 1.3f;
            
            return Mathf.Clamp(modifier, 0.1f, 2f);
        }

        private void AssignDefaultRole()
        {
            var unit = GetComponent<Unit>();
            if (unit == null) return;

            // Assign default role based on unit type
            string rolePath = GetDefaultRolePath(unit.UnitType);
            if (!string.IsNullOrEmpty(rolePath))
            {
                role = Resources.Load<UnitRole>(rolePath);
            }
        }

        private string GetDefaultRolePath(UnitType unitType)
        {
            return unitType switch
            {
                UnitType.Infantry => "Roles/Infantry_Assault",
                UnitType.LightVehicle => "Roles/Vehicle_Recon",
                UnitType.HeavyVehicle => "Roles/Vehicle_Support",
                UnitType.Tank => "Roles/Tank_Assault",
                UnitType.Artillery => "Roles/Artillery_Support",
                UnitType.Aircraft => "Roles/Aircraft_Recon",
                UnitType.Building => "Roles/Building_Defense",
                _ => "Roles/Default_Assault"
            };
        }

        private TacticalBehavior GetDefaultBehavior()
        {
            return new TacticalBehavior
            {
                aggressiveness = 0.5f,
                caution = 0.5f,
                supportOriented = false,
                independentOperations = true,
                formationDiscipline = 0.3f
            };
        }

        /// <summary>
        /// Check if this unit can coordinate with another unit
        /// </summary>
        public bool CanCoordinateWith(UnitRoleComponent otherRole)
        {
            if (role == null || otherRole?.role == null) return false;

            return role.SupportedRoles.Contains(otherRole.role.RoleType) ||
                   role.SupportingRoles.Contains(otherRole.role.RoleType) ||
                   otherRole.role.SupportedRoles.Contains(role.RoleType) ||
                   otherRole.role.SupportingRoles.Contains(role.RoleType);
        }

        /// <summary>
        /// Get the preferred engagement range for this unit
        /// </summary>
        public float GetPreferredEngagementRange()
        {
            if (role == null) return 15f;
            return role.PreferredEngagementRange;
        }

        /// <summary>
        /// Check if this unit requires support from other units
        /// </summary>
        public bool RequiresSupport()
        {
            if (role == null) return false;
            return role.RequiresSupport;
        }

        /// <summary>
        /// Check if this unit can provide fire support
        /// </summary>
        public bool CanProvideFireSupport()
        {
            if (role == null) return false;
            return role.CanProvideFireSupport;
        }

        /// <summary>
        /// Get the coordination radius for this unit
        /// </summary>
        public float GetCoordinationRadius()
        {
            if (role == null) return 25f;
            return role.CoordinationRadius;
        }

        // Debug information
        public string GetRoleInfo()
        {
            if (role == null) return "No Role Assigned";
            
            var behavior = GetTacticalBehavior();
            return $"{role.RoleName} ({role.RoleType})\n" +
                   $"Doctrine: {role.Doctrine}\n" +
                   $"Aggressiveness: {behavior.aggressiveness:F2}\n" +
                   $"Caution: {behavior.caution:F2}\n" +
                   $"Experience: {experienceModifier:F2}\n" +
                   $"Morale: {moraleModifier:F2}";
        }

        private void OnValidate()
        {
            // Clamp values in editor
            experienceModifier = Mathf.Clamp01(experienceModifier);
            moraleModifier = Mathf.Clamp01(moraleModifier);
            behaviorCached = false;
        }
    }
}
