using UnityEngine;

namespace ColdVor.RTS
{
    /// <summary>
    /// Base class for singleton MonoBehaviours
    /// Eliminates redundant singleton pattern implementations
    /// </summary>
    public abstract class SingletonBase<T> : MonoBehaviour where T : MonoBehaviour
    {
        private static T instance;
        public static T Instance => instance;

        protected virtual void Awake()
        {
            if (instance == null)
            {
                instance = this as T;
                DontDestroyOnLoad(gameObject);
                OnSingletonAwake();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        /// <summary>
        /// Called when singleton is first created
        /// Override this instead of Awake in derived classes
        /// </summary>
        protected virtual void OnSingletonAwake() { }

        protected virtual void OnDestroy()
        {
            if (instance == this)
            {
                instance = null;
            }
        }
    }
}
