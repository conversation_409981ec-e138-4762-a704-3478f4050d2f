using UnityEngine;

namespace ColdVor.RTS
{
    /// <summary>
    /// ScriptableObject for weapon configuration data.
    /// This is the single source of truth for all weapon parameters.
    /// Renamed from WeaponDataSO to just "Weapon" for cleaner naming.
    /// </summary>
    [CreateAssetMenu(fileName = "New Weapon", menuName = "ColdVor/Weapon")]
    public class Weapon : ScriptableObject
    {
        [Header("Basic Properties")]
        [SerializeField] private string weaponName = "Basic Weapon";
        [SerializeField] private WeaponType weaponType = WeaponType.Ballistic;
        [SerializeField] private string description = "";
        
        [Header("Combat Statistics")]
        [SerializeField] private int damage = 25;
        [SerializeField] private float range = 25f;
        [SerializeField] private float fireRate = 0.5f; // Shots per second
        [SerializeField] private float accuracy = 0.95f; // 0-1 accuracy rating
        
        [Header("Projectile Properties")]
        [SerializeField] private float projectileSpeed = 60f;
        [SerializeField] private GameObject projectilePrefab;
        [SerializeField] private bool useGravity = false;
        [SerializeField] private bool isHoming = false;
        
        [Header("Special Properties")]
        [SerializeField] private bool isExplosive = false;
        [SerializeField] private float explosionRadius = 0f;
        [SerializeField] private int penetration = 0; // How many targets it can penetrate
        
        [Header("Ammunition")]
        [SerializeField] private int magazineSize = -1; // -1 = unlimited
        [SerializeField] private float reloadTime = 2f;
        [SerializeField] private bool requiresAmmo = false;
        
        [Header("Audio")]
        [SerializeField] private AudioClip fireSound;
        [SerializeField] private AudioClip reloadSound;
        [SerializeField] private AudioClip dryFireSound;
        
        [Header("Visual Effects")]
        [SerializeField] private GameObject muzzleFlashPrefab;
        [SerializeField] private GameObject hitEffectPrefab;
        [SerializeField] private GameObject explosionEffectPrefab;
        
        [Header("Advanced Settings")]
        [SerializeField] private float spread = 0f; // Weapon spread in degrees
        [SerializeField] private int burstCount = 1; // Number of projectiles per shot
        [SerializeField] private float burstDelay = 0.1f; // Delay between burst shots
        [SerializeField] private bool canFireWhileMoving = true;
        [SerializeField] private float movementAccuracyPenalty = 0.1f;
        
        // Public properties (read-only access to private fields)
        public string WeaponName => weaponName;
        public WeaponType WeaponType => weaponType;
        public string Description => description;
        public int Damage => damage;
        public float Range => range;
        public float FireRate => fireRate;
        public float Accuracy => accuracy;
        public float ProjectileSpeed => projectileSpeed;
        public GameObject ProjectilePrefab => projectilePrefab;
        public bool UseGravity => useGravity;
        public bool IsHoming => isHoming;
        public bool IsExplosive => isExplosive;
        public float ExplosionRadius => explosionRadius;
        public int Penetration => penetration;
        public int MagazineSize => magazineSize;
        public float ReloadTime => reloadTime;
        public bool RequiresAmmo => requiresAmmo;
        public AudioClip FireSound => fireSound;
        public AudioClip ReloadSound => reloadSound;
        public AudioClip DryFireSound => dryFireSound;
        public GameObject MuzzleFlashPrefab => muzzleFlashPrefab;
        public GameObject HitEffectPrefab => hitEffectPrefab;
        public GameObject ExplosionEffectPrefab => explosionEffectPrefab;
        public float Spread => spread;
        public int BurstCount => burstCount;
        public float BurstDelay => burstDelay;
        public bool CanFireWhileMoving => canFireWhileMoving;
        public float MovementAccuracyPenalty => movementAccuracyPenalty;
        
        /// <summary>
        /// Get effective accuracy based on movement state
        /// </summary>
        public float GetEffectiveAccuracy(bool isMoving)
        {
            if (!isMoving || canFireWhileMoving)
                return accuracy;
                
            return Mathf.Max(0f, accuracy - movementAccuracyPenalty);
        }
        
        /// <summary>
        /// Get fire cooldown in seconds
        /// </summary>
        public float GetFireCooldown()
        {
            return fireRate > 0f ? (1f / fireRate) : 1f;
        }
        
        /// <summary>
        /// Check if this weapon can engage a target at the given distance
        /// </summary>
        public bool CanEngageAtDistance(float distance)
        {
            return distance <= range;
        }
        
        /// <summary>
        /// Validate weapon configuration
        /// </summary>
        public bool IsValid()
        {
            if (string.IsNullOrEmpty(weaponName))
                return false;
                
            if (damage <= 0)
                return false;
                
            if (range <= 0)
                return false;
                
            if (fireRate <= 0)
                return false;
                
            if (projectileSpeed <= 0 && (weaponType == WeaponType.Ballistic || weaponType == WeaponType.Explosive))
                return false;
                
            if (isExplosive && explosionRadius <= 0)
                return false;
                
            return true;
        }
        
        /// <summary>
        /// Create ProjectileParameters directly from this ScriptableObject
        /// </summary>
        public ProjectileParameters ToProjectileParameters()
        {
            return new ProjectileParameters
            {
                speed = this.projectileSpeed,
                useGravity = this.useGravity,
                isHoming = this.isHoming,
                homingStrength = 2f, // Default value, could be added to SO if needed
                weaponType = this.weaponType,
                damage = this.damage,
                isExplosive = this.isExplosive,
                explosionRadius = this.explosionRadius,
                accuracy = this.accuracy
            };
        }
        
        private void OnValidate()
        {
            // Clamp values to valid ranges
            damage = Mathf.Max(1, damage);
            range = Mathf.Max(0.1f, range);
            fireRate = Mathf.Max(0.1f, fireRate);
            accuracy = Mathf.Clamp01(accuracy);
            projectileSpeed = Mathf.Max(0.1f, projectileSpeed);
            explosionRadius = Mathf.Max(0f, explosionRadius);
            penetration = Mathf.Max(0, penetration);
            reloadTime = Mathf.Max(0.1f, reloadTime);
            spread = Mathf.Clamp(spread, 0f, 45f);
            burstCount = Mathf.Max(1, burstCount);
            burstDelay = Mathf.Max(0f, burstDelay);
            movementAccuracyPenalty = Mathf.Clamp01(movementAccuracyPenalty);
            
            // Validate logical constraints
            if (isExplosive && explosionRadius <= 0f)
            {
                explosionRadius = 2f; // Default explosion radius
            }
            
            if (weaponType == WeaponType.Energy)
            {
                isHoming = true; // Energy weapons are typically homing
            }
        }
    }
}
