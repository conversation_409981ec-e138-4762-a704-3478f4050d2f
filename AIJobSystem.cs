using Unity.Collections;
using Unity.Jobs;
using UnityEngine;
using System.Collections.Generic;

namespace ColdVor.RTS
{
    /// <summary>
    /// High-performance multi-threaded AI system using Unity Job System
    /// Handles hundreds/thousands of units efficiently
    /// </summary>
    public class AIJobSystem : SingletonBase<AIJobSystem>
    {
        [Header("Performance Settings")]
        [SerializeField] private int maxUnitsPerJob = 64;
        [SerializeField] private int jobBatchSize = 32;
        [SerializeField] private float aiUpdateInterval = 0.33f; // 3 FPS for AI decisions - truly independent processing
        
        // Native arrays for job data
        private NativeArray<AIUnitData> unitData;
        private NativeArray<AIDecisionResult> decisionResults;
        private NativeArray<Vector3> enemyPositions;
        private NativeArray<int> enemyFactions;
        
        // Unit tracking
        private List<AI> registeredUnits = new List<AI>();
        private Dictionary<AI, int> unitToIndex = new Dictionary<AI, int>();
        private float lastUpdateTime;
        private JobHandle currentJobHandle;
        
        // Job completion tracking
        private bool jobsRunning = false;

        // Frame-based staggering for truly independent processing
        private int frameOffset = 0;

        // Public API
        public int RegisteredUnitCount => registeredUnits.Count;

        protected override void OnSingletonAwake()
        {
            InitializeJobSystem();

            // Initialize frame offset for staggered processing
            frameOffset = Random.Range(0, 10); // Random start frame to spread processing
        }

        private void InitializeJobSystem()
        {
            // Initialize with reasonable capacity
            int initialCapacity = 1000;
            
            unitData = new NativeArray<AIUnitData>(initialCapacity, Allocator.Persistent);
            decisionResults = new NativeArray<AIDecisionResult>(initialCapacity, Allocator.Persistent);
            enemyPositions = new NativeArray<Vector3>(initialCapacity * 10, Allocator.Persistent); // 10 enemies per unit max
            enemyFactions = new NativeArray<int>(initialCapacity * 10, Allocator.Persistent);
            
            lastUpdateTime = Time.time;
        }

        private void Update()
        {
            if (registeredUnits.Count == 0) return;

            // Frame-based staggering: Only process on specific frames for this instance
            if ((Time.frameCount + frameOffset) % 20 != 0) return; // Process every 20 frames with offset

            // Check if enough time has passed for AI update
            if (Time.time - lastUpdateTime < aiUpdateInterval) return;

            // Complete previous jobs if still running
            if (jobsRunning)
            {
                currentJobHandle.Complete();
                ProcessJobResults();
                jobsRunning = false;
            }

            // Start new AI processing jobs
            StartAIJobs();
            lastUpdateTime = Time.time;
        }

        /// <summary>
        /// Register an AI unit for multi-threaded processing
        /// </summary>
        public void RegisterUnit(AI aiUnit)
        {
            if (unitToIndex.ContainsKey(aiUnit)) return;

            int index = registeredUnits.Count;
            registeredUnits.Add(aiUnit);
            unitToIndex[aiUnit] = index;

            // Expand arrays if needed
            if (index >= unitData.Length)
            {
                ExpandArrays();
            }
        }

        /// <summary>
        /// Unregister an AI unit
        /// </summary>
        public void UnregisterUnit(AI aiUnit)
        {
            if (!unitToIndex.ContainsKey(aiUnit)) return;

            int index = unitToIndex[aiUnit];
            int lastIndex = registeredUnits.Count - 1;

            // Swap with last element
            if (index != lastIndex)
            {
                AI lastUnit = registeredUnits[lastIndex];
                registeredUnits[index] = lastUnit;
                unitToIndex[lastUnit] = index;
            }

            registeredUnits.RemoveAt(lastIndex);
            unitToIndex.Remove(aiUnit);
        }

        private void StartAIJobs()
        {
            if (registeredUnits.Count == 0) return;

            // Update unit data for jobs
            UpdateUnitDataForJobs();

            // Create and schedule AI decision job
            var aiDecisionJob = new AIDecisionJob
            {
                unitData = unitData,
                enemyPositions = enemyPositions,
                enemyFactions = enemyFactions,
                decisionResults = decisionResults,
                deltaTime = Time.deltaTime,
                currentTime = Time.time
            };

            // Schedule job with appropriate batch size
            int unitCount = registeredUnits.Count;

            // Use maxUnitsPerJob to determine if we need multiple jobs
            if (unitCount > maxUnitsPerJob)
            {
                // For large unit counts, use smaller batch size for better distribution
                int adjustedBatchSize = Mathf.Max(1, jobBatchSize / 2);
                currentJobHandle = aiDecisionJob.Schedule(unitCount, adjustedBatchSize);
            }
            else
            {
                currentJobHandle = aiDecisionJob.Schedule(unitCount, jobBatchSize);
            }

            jobsRunning = true;
        }

        private void UpdateUnitDataForJobs()
        {
            int enemyIndex = 0;

            for (int i = 0; i < registeredUnits.Count; i++)
            {
                AI aiUnit = registeredUnits[i];
                if (aiUnit == null || aiUnit.unit == null) continue;

                var unit = aiUnit.unit;
                
                // Pack unit data for job with individual timing offset
                float unitOffset = (aiUnit.GetInstanceID() % 100) * 0.01f; // 0-1 second offset per unit

                unitData[i] = new AIUnitData
                {
                    position = unit.transform.position,
                    faction = (int)unit.Faction,
                    health = unit.CurrentHealth,
                    maxHealth = unit.MaxHealth,
                    weaponRange = unit.WeaponRange,
                    detectionRange = unit.DetectionRange,
                    currentState = (int)aiUnit.CurrentState,
                    suppressionLevel = aiUnit.SuppressionLevel,
                    moraleLevel = aiUnit.MoraleLevel,
                    enemyStartIndex = enemyIndex,
                    enemyCount = 0,
                    unitTimeOffset = unitOffset,
                    lastActionTime = aiUnit.LastActionTime
                };

                // Pack enemy data
                var enemies = unit.DetectedEnemies;
                int enemyCount = Mathf.Min(enemies.Count, 10); // Limit to 10 enemies per unit
                
                for (int e = 0; e < enemyCount; e++)
                {
                    if (enemies[e] != null)
                    {
                        enemyPositions[enemyIndex] = enemies[e].transform.position;
                        enemyFactions[enemyIndex] = (int)enemies[e].Faction;
                        enemyIndex++;
                    }
                }

                // Update enemy count
                var data = unitData[i];
                data.enemyCount = enemyCount;
                unitData[i] = data;
            }
        }

        private void ProcessJobResults()
        {
            for (int i = 0; i < registeredUnits.Count; i++)
            {
                AI aiUnit = registeredUnits[i];
                if (aiUnit == null) continue;

                var result = decisionResults[i];
                
                // Apply job results to AI unit
                aiUnit.ApplyJobResult(result);
            }
        }

        private void ExpandArrays()
        {
            int newSize = unitData.Length * 2;
            
            // Create new arrays
            var newUnitData = new NativeArray<AIUnitData>(newSize, Allocator.Persistent);
            var newDecisionResults = new NativeArray<AIDecisionResult>(newSize, Allocator.Persistent);
            var newEnemyPositions = new NativeArray<Vector3>(newSize * 10, Allocator.Persistent);
            var newEnemyFactions = new NativeArray<int>(newSize * 10, Allocator.Persistent);

            // Copy existing data
            NativeArray<AIUnitData>.Copy(unitData, newUnitData, unitData.Length);
            NativeArray<AIDecisionResult>.Copy(decisionResults, newDecisionResults, decisionResults.Length);
            NativeArray<Vector3>.Copy(enemyPositions, newEnemyPositions, enemyPositions.Length);
            NativeArray<int>.Copy(enemyFactions, newEnemyFactions, enemyFactions.Length);

            // Dispose old arrays
            unitData.Dispose();
            decisionResults.Dispose();
            enemyPositions.Dispose();
            enemyFactions.Dispose();

            // Assign new arrays
            unitData = newUnitData;
            decisionResults = newDecisionResults;
            enemyPositions = newEnemyPositions;
            enemyFactions = newEnemyFactions;
        }

        protected override void OnDestroy()
        {
            // Complete any running jobs
            if (jobsRunning)
            {
                currentJobHandle.Complete();
            }

            // Dispose native arrays
            if (unitData.IsCreated) unitData.Dispose();
            if (decisionResults.IsCreated) decisionResults.Dispose();
            if (enemyPositions.IsCreated) enemyPositions.Dispose();
            if (enemyFactions.IsCreated) enemyFactions.Dispose();

            // Call base class OnDestroy
            base.OnDestroy();
        }
    }

    /// <summary>
    /// Data structure for AI unit information in jobs
    /// </summary>
    public struct AIUnitData
    {
        public Vector3 position;
        public int faction;
        public int health;
        public int maxHealth;
        public float weaponRange;
        public float detectionRange;
        public int currentState;
        public float suppressionLevel;
        public float moraleLevel;
        public int enemyStartIndex;
        public int enemyCount;
        public float unitTimeOffset; // Individual timing offset for this unit
        public float lastActionTime; // When this unit last took an action
    }

    /// <summary>
    /// Result structure from AI decision jobs
    /// </summary>
    public struct AIDecisionResult
    {
        public int newState;
        public Vector3 moveTarget;
        public Vector3 attackTarget;
        public bool hasAttackTarget;
        public float aggressiveness;
        public float caution;
    }

    /// <summary>
    /// High-performance AI decision job
    /// </summary>
    public struct AIDecisionJob : IJobParallelFor
    {
        [ReadOnly] public NativeArray<AIUnitData> unitData;
        [ReadOnly] public NativeArray<Vector3> enemyPositions;
        [ReadOnly] public NativeArray<int> enemyFactions;
        [WriteOnly] public NativeArray<AIDecisionResult> decisionResults;
        [ReadOnly] public float deltaTime;
        [ReadOnly] public float currentTime;

        public void Execute(int index)
        {
            var unit = unitData[index];
            var result = new AIDecisionResult();

            // INDEPENDENT PROCESSING: Check if this unit should act based on its individual timing
            float timeSinceLastAction = currentTime - unit.lastActionTime;
            float requiredInterval = 0.5f + unit.unitTimeOffset; // Base interval + individual offset

            // Skip processing if this unit acted too recently (independent timing)
            if (timeSinceLastAction < requiredInterval)
            {
                // Don't change state or issue commands - maintain current behavior
                result.newState = unit.currentState;
                decisionResults[index] = result;
                return;
            }

            // Calculate health percentage
            float healthPercentage = (float)unit.health / unit.maxHealth;

            // Find closest enemy
            float closestEnemyDistance = float.MaxValue;
            Vector3 closestEnemyPosition = Vector3.zero;
            bool hasEnemy = false;

            for (int i = 0; i < unit.enemyCount; i++)
            {
                int enemyIndex = unit.enemyStartIndex + i;
                if (enemyIndex >= enemyPositions.Length) break;

                Vector3 enemyPos = enemyPositions[enemyIndex];
                int enemyFaction = enemyFactions[enemyIndex];

                // Skip same faction
                if (enemyFaction == unit.faction) continue;

                float distance = Vector3.Distance(unit.position, enemyPos);
                if (distance < closestEnemyDistance)
                {
                    closestEnemyDistance = distance;
                    closestEnemyPosition = enemyPos;
                    hasEnemy = true;
                }
            }

            // AI Decision Logic (simplified for performance)
            if (healthPercentage < 0.25f)
            {
                // Retreat
                result.newState = 4; // AIState.Retreat
                if (hasEnemy)
                {
                    Vector3 retreatDirection = (unit.position - closestEnemyPosition).normalized;
                    result.moveTarget = unit.position + retreatDirection * 20f;
                }
            }
            else if (hasEnemy && closestEnemyDistance <= unit.weaponRange)
            {
                // Engage
                result.newState = 2; // AIState.Engage
                result.attackTarget = closestEnemyPosition;
                result.hasAttackTarget = true;

                // Calculate optimal engagement position with unit separation
                float baseOptimalRange = unit.weaponRange * 0.6f; // Base engagement range
                float minRange = unit.weaponRange * 0.4f; // Minimum safe distance
                float movementThreshold = 5f; // Larger threshold to prevent micro-movements

                // Add unit-specific offset to prevent convergence
                float unitOffset = (index % 4) * 10f; // 0, 10, 20, 30 unit spread
                float optimalRange = baseOptimalRange + unitOffset;

                if (closestEnemyDistance < minRange)
                {
                    // Too close, back away to optimal range with lateral offset
                    Vector3 direction = (unit.position - closestEnemyPosition).normalized;

                    // Add lateral offset to prevent units from backing away in the same line
                    Vector3 lateralOffset = Vector3.Cross(direction, Vector3.up) * (index % 2 == 0 ? 15f : -15f);
                    Vector3 targetPos = closestEnemyPosition + direction * optimalRange + lateralOffset;

                    float distanceToTarget = Vector3.Distance(unit.position, targetPos);
                    if (distanceToTarget > movementThreshold)
                    {
                        result.moveTarget = targetPos;

                        // DEBUG: Log positioning decisions
                        if (index < 2) // Only log first 2 units to avoid spam
                        {
                            Debug.Log($"[AIJob] Unit {index}: Too close ({closestEnemyDistance:F1}), backing to {targetPos} (range: {optimalRange:F1})");
                        }
                    }
                }
                else if (closestEnemyDistance > unit.weaponRange * 0.8f)
                {
                    // Too far, move closer but with unit-specific positioning
                    Vector3 direction = (closestEnemyPosition - unit.position).normalized;

                    // Add lateral spread to prevent units from converging on same point
                    Vector3 lateralOffset = Vector3.Cross(direction, Vector3.up) * (index % 2 == 0 ? 10f : -10f);
                    Vector3 targetPos = closestEnemyPosition - direction * optimalRange + lateralOffset;

                    float distanceToTarget = Vector3.Distance(unit.position, targetPos);
                    if (distanceToTarget > movementThreshold)
                    {
                        result.moveTarget = targetPos;

                        // DEBUG: Log positioning decisions
                        if (index < 2) // Only log first 2 units to avoid spam
                        {
                            Debug.Log($"[AIJob] Unit {index}: Too far ({closestEnemyDistance:F1}), moving to {targetPos} (range: {optimalRange:F1})");
                        }
                    }
                }
                // If within optimal range, maintain position
            }
            else if (hasEnemy && closestEnemyDistance <= unit.detectionRange)
            {
                // Investigate - move to engagement range with unit separation
                result.newState = 3; // AIState.Investigate
                float baseOptimalRange = unit.weaponRange * 0.7f;
                float unitOffset = (index % 4) * 8f; // Smaller offset for investigation
                float optimalRange = baseOptimalRange + unitOffset;

                if (closestEnemyDistance > optimalRange + 10f) // Only move if significantly outside range
                {
                    // Move to optimal engagement position with lateral spread
                    Vector3 direction = (closestEnemyPosition - unit.position).normalized;
                    Vector3 lateralOffset = Vector3.Cross(direction, Vector3.up) * (index % 2 == 0 ? 8f : -8f);
                    result.moveTarget = closestEnemyPosition - direction * optimalRange + lateralOffset;
                }
            }
            else
            {
                // Patrol - only generate new patrol target occasionally
                result.newState = 1; // AIState.Patrol
                // Generate patrol movement less frequently to prevent stuttering
                float patrolTime = Mathf.Floor(currentTime / 3f) * 3f; // Change patrol target every 3 seconds
                float time = patrolTime * 0.1f + index * 0.1f; // Offset per unit
                Vector3 patrolTarget = unit.position + new Vector3(Mathf.Sin(time) * 15f, 0, Mathf.Cos(time) * 15f);

                // Only set patrol target if far enough from current position
                if (Vector3.Distance(unit.position, patrolTarget) > 8f)
                {
                    result.moveTarget = patrolTarget;
                }
            }

            // Calculate behavior modifiers
            result.aggressiveness = Mathf.Max(0.1f, 1.0f - unit.suppressionLevel * 0.01f);
            result.caution = Mathf.Min(2.0f, 0.5f + unit.suppressionLevel * 0.01f);

            decisionResults[index] = result;
        }
    }
}
