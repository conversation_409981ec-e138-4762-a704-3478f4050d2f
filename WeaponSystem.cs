using UnityEngine;
using System;

namespace ColdVor.RTS
{
    /// <summary>
    /// Modular weapon system component that handles all weapon-related functionality.
    /// This is the single source of truth for weapon parameters and behavior.
    /// </summary>
    public class WeaponSystem : MonoBehaviour
    {
        [Header("Weapon System Settings")]
        [SerializeField] private Transform firePoint;
        // WeaponSystem reads weapon data from Unit component
        
        [Header("Ammunition")]
        [SerializeField] private int maxAmmo = -1; // -1 = unlimited
        [SerializeField] private float reloadTime = 2f;
        [SerializeField] private bool autoReload = true;
        
        [Header("Audio")]
        [SerializeField] private AudioClip fireSound;
        [SerializeField] private AudioClip reloadSound;
        [SerializeField] private AudioSource audioSource;
        
        // Weapon state
        private int currentAmmo;
        private float lastFireTime;
        private float reloadStartTime;
        private WeaponState currentState = WeaponState.Ready;
        private Unit ownerUnit;

        // Staggered firing to prevent synchronous shots
        private float fireOffset;
        
        // Cached components
        private VisionSystem visionSystem;
        
        // Events
        public event Action<WeaponSystem, Unit> OnWeaponFired;
        public event Action<WeaponSystem> OnWeaponReloaded;
        public event Action<WeaponSystem> OnAmmoEmpty;
        public event Action<WeaponSystem, WeaponState> OnWeaponStateChanged;
        
        // Properties (read from Unit)
        public Weapon CurrentWeapon => ownerUnit?.UnitWeapon;
        public bool CanFire => currentState == WeaponState.Ready && HasAmmo && !IsOnCooldown;
        public float TimeToNextShot => Mathf.Max(0f, GetFireCooldown() - (Time.time - lastFireTime));
        public int CurrentAmmo => currentAmmo;
        public int MaxAmmo => maxAmmo;
        public WeaponState State => currentState;
        public Vector3 FirePosition => firePoint != null ? firePoint.position : transform.position;

        // Calculated properties
        public bool HasAmmo => maxAmmo < 0 || currentAmmo > 0;
        public bool IsOnCooldown => Time.time - lastFireTime < GetFireCooldown();
        public float GetFireCooldown() => CurrentWeapon != null ? CurrentWeapon.GetFireCooldown() : 1f;
        public float WeaponRange => CurrentWeapon?.Range ?? 0f;
        public bool AutoAcquireTargets => ownerUnit?.AutoAcquireTargets ?? false;
        
        private void Awake()
        {
            ownerUnit = GetComponent<Unit>();
            visionSystem = GetComponent<VisionSystem>();
            
            if (audioSource == null)
            {
                audioSource = GetComponent<AudioSource>();
                if (audioSource == null)
                {
                    audioSource = gameObject.AddComponent<AudioSource>();
                }
            }
            
            InitializeWeapon();
        }
        
        private void Start()
        {
            // Subscribe to vision system events if available
            if (visionSystem != null)
            {
                visionSystem.OnEnemyDetected += OnEnemyDetected;
                visionSystem.OnEnemyLost += OnEnemyLost;
            }
        }
        
        private void Update()
        {
            UpdateWeaponState();
        }
        
        private void OnDestroy()
        {
            // Unsubscribe from events
            if (visionSystem != null)
            {
                visionSystem.OnEnemyDetected -= OnEnemyDetected;
                visionSystem.OnEnemyLost -= OnEnemyLost;
            }
        }
        
        /// <summary>
        /// Initialize weapon with current weapon data
        /// </summary>
        private void InitializeWeapon()
        {
            if (CurrentWeapon == null)
            {
                Debug.LogWarning($"WeaponSystem on {gameObject.name}: No weapon data assigned!");
                return;
            }

            // Set ammo from weapon data if not overridden
            if (maxAmmo == -1 && CurrentWeapon.RequiresAmmo)
            {
                maxAmmo = CurrentWeapon.MagazineSize;
            }

            currentAmmo = maxAmmo < 0 ? -1 : maxAmmo;
            currentState = WeaponState.Ready;
            lastFireTime = 0f;

            // Initialize staggered firing offset (0-0.5 seconds)
            fireOffset = (GetInstanceID() % 50) * 0.01f;

            if (DebugManager.Instance != null)
            {
                DebugManager.Instance.LogSystem("WeaponSystem",
                    $"Initialized {CurrentWeapon.WeaponName} on {gameObject.name}");
            }
        }
        
        /// <summary>
        /// Update weapon state (reloading, cooldowns, etc.)
        /// </summary>
        private void UpdateWeaponState()
        {
            switch (currentState)
            {
                case WeaponState.Reloading:
                    if (Time.time - reloadStartTime >= reloadTime)
                    {
                        CompleteReload();
                    }
                    break;
                    
                case WeaponState.Ready:
                    // Check if we need to auto-reload
                    if (autoReload && !HasAmmo && maxAmmo > 0)
                    {
                        StartReload();
                    }
                    break;
            }
        }
        
        /// <summary>
        /// Attempt to fire the weapon at a target position
        /// </summary>
        public bool TryFire(Vector3 targetPosition, Unit target = null)
        {
            if (!CanFire)
            {
                return false;
            }

            if (CurrentWeapon == null)
            {
                Debug.LogError($"WeaponSystem.TryFire: No weapon data on {gameObject.name}");
                return false;
            }

            // Check range
            float distance = Vector3.Distance(FirePosition, targetPosition);
            if (distance > CurrentWeapon.Range)
            {
                return false;
            }

            // Add staggered firing delay to prevent synchronous shots
            float timeSinceLastFire = Time.time - lastFireTime;
            float requiredDelay = GetFireCooldown() + fireOffset;

            if (timeSinceLastFire < requiredDelay)
            {
                return false; // Still in cooldown + offset
            }

            // Perform the fire
            ExecuteFire(targetPosition, target);
            return true;
        }
        
        /// <summary>
        /// Execute the actual firing logic
        /// </summary>
        private void ExecuteFire(Vector3 targetPosition, Unit target)
        {
            lastFireTime = Time.time;
            
            // Consume ammo
            if (maxAmmo > 0)
            {
                currentAmmo--;
                if (currentAmmo <= 0)
                {
                    OnAmmoEmpty?.Invoke(this);
                }
            }
            
            // Request projectile creation from CombatSystem using new parameter system
            if (CombatSystem.Instance != null)
            {
                ProjectileParameters parameters = CurrentWeapon.ToProjectileParameters();

                if (CurrentWeapon.WeaponType == WeaponType.Ballistic ||
                    CurrentWeapon.WeaponType == WeaponType.Explosive ||
                    CurrentWeapon.WeaponType == WeaponType.Guided)
                {
                    // Use the new projectile system with parameters and prefab
                    CombatSystem.Instance.FireProjectileWithParameters(ownerUnit, target, targetPosition, parameters, CurrentWeapon.ProjectilePrefab);
                }
                else
                {
                    // For instant hit weapons, we still need the old method temporarily
                    CombatSystem.Instance.FireInstantHitWithParameters(ownerUnit, target, targetPosition, parameters);
                }
            }
            
            // Play audio
            PlayFireSound();
            
            // Trigger events
            OnWeaponFired?.Invoke(this, target);
            
            if (DebugManager.Instance != null)
            {
                DebugManager.Instance.LogCombat($"{gameObject.name} fired {CurrentWeapon.WeaponName}");
            }
        }
        
        /// <summary>
        /// Start reloading the weapon
        /// </summary>
        public void StartReload()
        {
            if (currentState == WeaponState.Reloading || maxAmmo < 0)
            {
                return;
            }
            
            SetWeaponState(WeaponState.Reloading);
            reloadStartTime = Time.time;
            
            PlayReloadSound();
            
            if (DebugManager.Instance != null)
            {
                DebugManager.Instance.LogCombat($"{gameObject.name} started reloading");
            }
        }
        
        /// <summary>
        /// Complete the reload process
        /// </summary>
        private void CompleteReload()
        {
            currentAmmo = maxAmmo;
            SetWeaponState(WeaponState.Ready);
            
            OnWeaponReloaded?.Invoke(this);
            
            if (DebugManager.Instance != null)
            {
                DebugManager.Instance.LogCombat($"{gameObject.name} finished reloading");
            }
        }
        
        /// <summary>
        /// Refresh weapon data (called when Unit's weapon changes)
        /// </summary>
        public void RefreshWeaponData()
        {
            InitializeWeapon();
        }
        
        /// <summary>
        /// Set weapon state and trigger events
        /// </summary>
        private void SetWeaponState(WeaponState newState)
        {
            if (currentState != newState)
            {
                currentState = newState;
                OnWeaponStateChanged?.Invoke(this, newState);
            }
        }
        
        /// <summary>
        /// Handle enemy detection from vision system
        /// </summary>
        private void OnEnemyDetected(VisionSystem vision, Unit enemy)
        {
            // Auto-targeting logic could go here
            if (AutoAcquireTargets && CanFire)
            {
                // This would integrate with a targeting system
            }
        }
        
        /// <summary>
        /// Handle enemy lost from vision system
        /// </summary>
        private void OnEnemyLost(VisionSystem vision, Unit enemy)
        {
            // Handle target loss
        }
        
        /// <summary>
        /// Play weapon fire sound
        /// </summary>
        private void PlayFireSound()
        {
            if (audioSource != null && fireSound != null)
            {
                audioSource.PlayOneShot(fireSound);
            }
        }
        
        /// <summary>
        /// Play weapon reload sound
        /// </summary>
        private void PlayReloadSound()
        {
            if (audioSource != null && reloadSound != null)
            {
                audioSource.PlayOneShot(reloadSound);
            }
        }
    }
    
    /// <summary>
    /// Weapon state enumeration
    /// </summary>
    public enum WeaponState
    {
        Ready,      // Ready to fire
        Reloading,  // Currently reloading
        Jammed,     // Weapon malfunction
        Disabled    // Weapon disabled/destroyed
    }
}
