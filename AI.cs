using System.Collections.Generic;
using UnityEngine;

namespace ColdVor.RTS
{
    /// <summary>
    /// AI state enumeration for state machine behavior
    /// </summary>
    public enum AIState
    {
        Idle,           // Not doing anything
        Patrol,         // Patrolling assigned area
        Investigate,    // Investigating point of interest
        Engage,         // Engaging enemy targets
        Assault,        // Aggressive attack mode
        Retreat,        // Withdrawing from combat
        Defend,         // Holding defensive position
        Support         // Supporting other units
    }

    /// <summary>
    /// Generalized AI Controller for all autonomous units (friendly and enemy)
    /// Handles decision trees, tactical behavior, patrol routes, and combat engagement
    /// </summary>
    [RequireComponent(typeof(Unit))]
    public class AI : MonoBehaviour
    {
        [Head<PERSON>("AI Behavior")]
        [SerializeField] private AIState initialState = AIState.Patrol;
        [SerializeField] private float stateUpdateInterval = 1f; // Reduced frequency to prevent stuttering
        [SerializeField] private bool useJobSystem = true; // Enable multi-threaded AI
        // decisionMakingRange now uses Unit.DetectionRange

        [Header("Debug")]
        [SerializeField] private bool enableDetailedLogging = false;

        [Head<PERSON>("Patrol Settings")]
        [SerializeField] private Transform[] patrolPoints;
        [SerializeField] private bool randomPatrol = false;
        [SerializeField] private float randomPatrolRadius = 15f;

        [Header("Combat Settings")]
        [SerializeField] private float optimalFireRange = 15f;
        // minimumFireRange removed - use Weapon.MinimumRange from WeaponSystem
        [SerializeField] private float pursuitRange = 25f;
        [SerializeField] private float retreatHealthThreshold = 0.25f;

        [Header("Group Behavior")]
        [SerializeField] private bool useGroupBehavior = true;
        // groupCommunicationRange now uses Unit.CommunicationRange
        [SerializeField] private float reinforcementCallRange = 30f;

        [Header("Suppression & Morale")]
        [SerializeField] private float suppressionDecayRate = 10f; // Per second
        [SerializeField] private float moraleRecoveryRate = 5f; // Per second
        [SerializeField] private float maxSuppressionLevel = 100f;
        [SerializeField] private float maxMoraleLevel = 100f;

        // State machine
        private AIState currentState = AIState.Idle;
        private AIState previousState = AIState.Idle;
        private float stateEnterTime;
        private float lastStateUpdate;

        // Patrol state
        private int currentPatrolIndex = 0;
        private Vector3 randomPatrolCenter;
        private float lastPatrolMoveTime = 0f;
        private float patrolMoveInterval = 3f; // Wait 3 seconds between patrol moves

        // Combat state
        private Unit primaryTarget;
        private Vector3 lastKnownEnemyPosition;
        private float lastEnemyContactTime;
        private float suppressionLevel = 0f;
        private float moraleLevel = 100f;

        // Movement and positioning
        private Vector3 homePosition;
        public Unit unit; // Made public for system access
        private UnitRoleComponent roleComponent;
        private CombinedArmsGroup assignedGroup;

        // Cached perception data to reduce heavy queries
        private TacticalDecision cachedTacticalRecommendation;
        private TacticalOrder cachedCommandOrder;
        private CombinedArmsOperation cachedGroupRecommendation;

        // Job system integration
        private bool registeredWithJobSystem = false;
        private AIDecisionResult pendingJobResult;

        // Movement smoothing
        private float lastMovementCommandTime = 0f;
        private float movementCooldown = 0.3f; // Reduced cooldown for smoother movement

        // Individual unit timing for job system
        private float lastActionTime = 0f;

        // Public properties for TacticalAwareness to access AI configuration
        public float RandomPatrolRadius => randomPatrolRadius;
        public float OptimalFireRange => optimalFireRange;
        public bool RandomPatrol => randomPatrol;
        // MinimumFireRange removed - TacticalAwareness should use unit.WeaponSystem.CurrentWeapon.MinimumRange

        // Staggered updates for performance
        private float updateOffset;
        private float lastSuppressionUpdate;
        private float lastGroupCoordinationUpdate;
        private float combatUpdateOffset; // For staggered combat decisions
        private float lastPerceptionUpdate; // For heavy perception queries

        // Detection and awareness
        private List<Unit> knownEnemies = new List<Unit>();

        // Group coordination
        private List<AI> nearbyAllies = new List<AI>();
        private bool hasCalledForReinforcements = false;
        
        // Tactical decision tracking removed - using perception-based approach

        // Events
        public System.Action<AI, AIState, AIState> OnStateChanged;
        public System.Action<AI, Unit> OnEnemyEngaged;
        public System.Action<AI> OnRetreatStarted;

        // Properties
        public AIState CurrentState => currentState;
        public Unit PrimaryTarget => primaryTarget;
        public Vector3 HomePosition => homePosition;
        public float LastReinforcementCall { get; private set; } = -30f;

        private void Awake()
        {
            unit = GetComponent<Unit>();
            if (unit == null)
            {
                Debug.LogError($"AI on {gameObject.name} could not find Unit component!");
                return;
            }

            homePosition = transform.position;
            randomPatrolCenter = homePosition;

            // Initialize staggered update system
            InitializeStaggeredUpdates();

            // Subscribe to unit events
            unit.OnEnemyDetected += OnEnemyDetected;
            unit.OnEnemyLost += OnEnemyLost;
            unit.OnHealthChanged += OnHealthChanged;

            // TacticalAwareness is now a perception system - no event subscription needed
            // AI will actively read tactical recommendations during decision making
        }

        private void Start()
        {
            // Initialize role component
            roleComponent = GetComponent<UnitRoleComponent>();
            if (roleComponent == null && enableDetailedLogging)
            {
                Debug.LogWarning($"AI component on {gameObject.name} has no UnitRoleComponent - using default behavior");
            }

            // Validate NavMeshAgent
            if (unit != null && unit.NavAgent == null)
            {
                Debug.LogError($"AI on {gameObject.name}: Unit has no NavMeshAgent!");
                return;
            }

            ChangeState(initialState);

            // Initialize random patrol center if using random patrol
            if (randomPatrol && patrolPoints == null || patrolPoints.Length == 0)
            {
                randomPatrolCenter = homePosition;
            }

            // Register with Combined Arms Coordinator
            if (CombinedArmsCoordinator.Instance != null)
            {
                // Coordinator will automatically group this unit with compatible units
            }

            // Register with Job System for multi-threaded AI
            if (useJobSystem && AIJobSystem.Instance != null)
            {
                AIJobSystem.Instance.RegisterUnit(this);
                registeredWithJobSystem = true;
            }
        }

        /// <summary>
        /// Initialize staggered update system to spread AI processing across frames
        /// </summary>
        private void InitializeStaggeredUpdates()
        {
            // Spread AI updates across frames based on instance ID
            updateOffset = (GetInstanceID() % 10) * 0.1f;
            lastStateUpdate = Time.time + updateOffset;
            lastSuppressionUpdate = Time.time + updateOffset;
            lastGroupCoordinationUpdate = Time.time + updateOffset;
            lastPerceptionUpdate = Time.time + updateOffset;

            // Stagger combat decisions to prevent synchronous firing
            combatUpdateOffset = (GetInstanceID() % 20) * 0.05f; // 0-1 second spread

            // Initialize perception cache
            UpdatePerceptionCache();
        }

        private void OnDestroy()
        {
            // Unregister from job system
            if (registeredWithJobSystem && AIJobSystem.Instance != null)
            {
                AIJobSystem.Instance.UnregisterUnit(this);
            }

            // Unsubscribe from events
            if (unit != null)
            {
                unit.OnEnemyDetected -= OnEnemyDetected;
                unit.OnEnemyLost -= OnEnemyLost;
                unit.OnHealthChanged -= OnHealthChanged;
            }

            // No event unsubscription needed - TacticalAwareness is now a perception system
        }

        private void Update()
        {
            // Don't update AI if unit is dead
            if (unit == null || unit.IsDead)
            {
                return;
            }

            // Update suppression and morale (lightweight, every frame)
            UpdateSuppressionAndMorale();

            if (useJobSystem && registeredWithJobSystem)
            {
                // Job system handles decision making - AI just executes current state
                // Only update state behavior, don't make new decisions
                UpdateCurrentState();
            }
            else
            {
                // Fallback to traditional AI updates with proper staggering
                // Staggered state machine updates
                if (Time.time - lastStateUpdate >= stateUpdateInterval)
                {
                    UpdateStateMachine();
                    lastStateUpdate = Time.time;
                }

                // Update current state behavior (lightweight, every frame)
                UpdateCurrentState();

                // Staggered group coordination updates (heavier operation) - less frequent
                if (useGroupBehavior && Time.time - lastGroupCoordinationUpdate >= (stateUpdateInterval * 2f))
                {
                    UpdateGroupCoordination();
                    lastGroupCoordinationUpdate = Time.time;
                }
            }
        }

        #region State Machine

        /// <summary>
        /// Main state machine update - determines next state based on current situation
        /// </summary>
        private void UpdateStateMachine()
        {
            AIState newState = DetermineNextState();

            if (newState != currentState)
            {
                ChangeState(newState);
            }

            // Debug logging (reduced frequency)
            if (DebugManager.Instance != null && Time.frameCount % 60 == 0) // Only log every 60 frames (1 second at 60 FPS)
            {
                DebugManager.Instance.LogAI($"{unit.name}: State={currentState}, Enemies={unit.DetectedEnemies.Count}, Target={primaryTarget?.name ?? "null"}");
            }
        }

        /// <summary>
        /// SINGLE AI DECISION MAKER - Reads all perception data and makes ONE decision
        /// </summary>
        private AIState DetermineNextState()
        {
            // STEP 1: Gather behavior data (AI is the single decision maker)
            TacticalBehavior behavior = GetTacticalBehavior();
            // TacticalAwareness is now information-only - no recommendations processed

            // STEP 2: Critical health check (always takes priority)
            float healthPercentage = (float)unit.CurrentHealth / unit.MaxHealth;
            float retreatThreshold = retreatHealthThreshold * (1f + behavior.caution * 0.5f);
            if (healthPercentage <= retreatThreshold)
            {
                return AIState.Retreat;
            }

            // STEP 3: AI makes its own tactical decisions (single decision maker)
            // No external recommendations - AI analyzes situation directly

            // STEP 4: Direct threat response (immediate danger)
            if (unit.DetectedEnemies.Count > 0)
            {
                Unit closestEnemy = unit.GetClosestEnemy();
                if (closestEnemy != null)
                {
                    float distance = Vector3.Distance(transform.position, closestEnemy.transform.position);
                    float preferredRange = GetPreferredEngagementRange();

                    // Set target for combat system
                    if (primaryTarget != closestEnemy)
                    {
                        SetPrimaryTarget(closestEnemy);
                        unit.SetTarget(closestEnemy);
                    }

                    // Role-based engagement logic
                    if (ShouldEngageAtCurrentRange(distance, preferredRange, behavior))
                    {
                        return AIState.Engage;
                    }
                    else if (distance <= unit.DetectionRange)
                    {
                        return AIState.Investigate;
                    }
                }
            }

            // STEP 5: Default behavior
            if (currentState == AIState.Engage || currentState == AIState.Investigate || currentState == AIState.Retreat)
            {
                return AIState.Patrol;
            }

            return currentState;
        }

        /// <summary>
        /// Convert tactical recommendation to AI state
        /// </summary>
        private AIState ConvertTacticalRecommendationToState(TacticalDecision recommendation)
        {
            switch (recommendation.decisionType)
            {
                case TacticalDecisionType.Engage:
                    return AIState.Engage;
                case TacticalDecisionType.Retreat:
                    return AIState.Retreat;
                case TacticalDecisionType.Reposition:
                case TacticalDecisionType.Flank:
                    return AIState.Investigate; // Use investigate for movement
                case TacticalDecisionType.SupportAlly:
                    return AIState.Support;
                default:
                    return AIState.Idle;
            }
        }

        /// <summary>
        /// Process tactical recommendation (information only - AI makes final decision)
        /// </summary>
        private void ExecuteTacticalRecommendation(TacticalDecision recommendation)
        {
            switch (recommendation.decisionType)
            {
                case TacticalDecisionType.Engage:
                    if (recommendation.targetUnit != null)
                    {
                        SetPrimaryTarget(recommendation.targetUnit);
                        unit.SetTarget(recommendation.targetUnit);
                        // AI will handle movement decisions in UpdateEngageState()
                    }
                    break;

                case TacticalDecisionType.Reposition:
                    // DO NOT EXECUTE MOVEMENT - AI makes its own movement decisions
                    // This is just information that AI can consider
                    if (enableDetailedLogging)
                    {
                        Debug.Log($"AI {gameObject.name}: Received reposition recommendation to {recommendation.targetPosition}, but AI makes own movement decisions");
                    }
                    break;

                case TacticalDecisionType.Flank:
                    // DO NOT EXECUTE MOVEMENT - AI makes its own movement decisions
                    // This is just information that AI can consider
                    if (enableDetailedLogging)
                    {
                        Debug.Log($"AI {gameObject.name}: Received flank recommendation to {recommendation.targetPosition}, but AI makes own movement decisions");
                    }
                    break;

                case TacticalDecisionType.SupportAlly:
                    // DO NOT EXECUTE MOVEMENT - AI makes its own movement decisions
                    // This is just information that AI can consider
                    if (enableDetailedLogging)
                    {
                        Debug.Log($"AI {gameObject.name}: Received support ally recommendation for {recommendation.targetUnit?.name}, but AI makes own movement decisions");
                    }
                    break;

                case TacticalDecisionType.Retreat:
                    // Retreat logic handled by state machine
                    break;
            }
        }

        /// <summary>
        /// Get tactical behavior from role component or default
        /// </summary>
        private TacticalBehavior GetTacticalBehavior()
        {
            if (roleComponent != null)
            {
                return roleComponent.GetTacticalBehavior();
            }

            // Default behavior if no role component
            return new TacticalBehavior
            {
                aggressiveness = 0.5f,
                caution = 0.5f,
                supportOriented = false,
                independentOperations = true,
                formationDiscipline = 0.3f
            };
        }

        /// <summary>
        /// Get tactical recommendation from TacticalAwareness (cached for performance)
        /// </summary>
        private TacticalDecision GetTacticalRecommendation()
        {
            // Update cache less frequently to reduce stuttering
            if (Time.time - lastPerceptionUpdate > 1f)
            {
                UpdatePerceptionCache();
            }
            return cachedTacticalRecommendation;
        }

        /// <summary>
        /// Get command order from CommandHierarchy (cached for performance)
        /// </summary>
        private TacticalOrder GetCommandOrder()
        {
            // Use cached data
            return cachedCommandOrder;
        }

        /// <summary>
        /// Get group recommendation from CombinedArmsCoordinator (cached for performance)
        /// </summary>
        private CombinedArmsOperation GetGroupRecommendation()
        {
            // Use cached data
            return cachedGroupRecommendation;
        }

        /// <summary>
        /// Update perception cache (heavy operations done less frequently)
        /// </summary>
        private void UpdatePerceptionCache()
        {
            // Cache tactical recommendation
            if (TacticalAwareness.Instance != null)
            {
                cachedTacticalRecommendation = TacticalAwareness.Instance.GetLatestRecommendation(unit);
            }

            // Cache command order
            if (CommandHierarchy.Instance != null)
            {
                cachedCommandOrder = CommandHierarchy.Instance.GetCurrentOrder(unit);
            }

            // Cache group recommendation
            if (CombinedArmsCoordinator.Instance != null)
            {
                cachedGroupRecommendation = CombinedArmsCoordinator.Instance.GetGroupRecommendation(unit);
            }

            lastPerceptionUpdate = Time.time;
        }

        /// <summary>
        /// Convert group operation to AI state
        /// </summary>
        private AIState ConvertGroupOperationToState(CombinedArmsOperation operation)
        {
            return operation switch
            {
                CombinedArmsOperation.Assault => AIState.Engage,
                CombinedArmsOperation.Defense => AIState.Patrol,
                CombinedArmsOperation.SupportedAdvance => AIState.Investigate,
                CombinedArmsOperation.Reconnaissance => AIState.Investigate,
                CombinedArmsOperation.Withdrawal => AIState.Retreat,
                CombinedArmsOperation.Regroup => AIState.Patrol,
                _ => AIState.Idle
            };
        }

        /// <summary>
        /// Get preferred engagement range from role or default
        /// </summary>
        private float GetPreferredEngagementRange()
        {
            if (roleComponent != null)
            {
                return roleComponent.GetPreferredEngagementRange();
            }
            return optimalFireRange;
        }

        /// <summary>
        /// Determine if unit should engage at current range based on role
        /// </summary>
        private bool ShouldEngageAtCurrentRange(float distance, float preferredRange, TacticalBehavior behavior)
        {
            // Calculate optimal engagement range based on weapon and role
            float weaponRange = unit.WeaponRange;
            float minSafeDistance = weaponRange * 0.4f; // Stay back from minimum range
            float maxEngageDistance = Mathf.Min(preferredRange, weaponRange * 0.9f); // Don't exceed weapon effectiveness

            // Aggressive units engage at longer ranges within weapon effectiveness
            if (behavior.aggressiveness > 0.6f)
            {
                maxEngageDistance = weaponRange * 0.85f;
            }

            // Cautious units prefer closer, more accurate shots
            if (behavior.caution > 0.6f)
            {
                maxEngageDistance = weaponRange * 0.7f;
                minSafeDistance = weaponRange * 0.5f;
            }

            // Only engage if within effective range and not too close
            return distance <= maxEngageDistance && distance >= minSafeDistance;
        }

        /// <summary>
        /// Convert command order to AI state
        /// </summary>
        private AIState ConvertOrderToState(TacticalOrder order)
        {
            return order.orderType switch
            {
                TacticalOrderType.Attack => AIState.Engage,
                TacticalOrderType.Defend => AIState.Patrol,
                TacticalOrderType.Regroup => AIState.Investigate,
                TacticalOrderType.Retreat => AIState.Retreat,
                TacticalOrderType.Patrol => AIState.Patrol,
                TacticalOrderType.Support => AIState.Investigate,
                _ => AIState.Idle
            };
        }

        /// <summary>
        /// Process command order (information only - AI makes final movement decisions)
        /// </summary>
        private void ExecuteCommandOrder(TacticalOrder order)
        {
            switch (order.orderType)
            {
                case TacticalOrderType.Attack:
                    if (order.targetUnits.Count > 0)
                    {
                        Unit target = GetClosestTarget(order.targetUnits);
                        if (target != null)
                        {
                            SetPrimaryTarget(target);
                            unit.SetTarget(target);
                            // AI will handle movement in UpdateEngageState()
                        }
                    }
                    else if (order.targetPosition != Vector3.zero)
                    {
                        // Store attack position for AI to consider, but don't execute movement
                        lastKnownEnemyPosition = order.targetPosition;
                        ChangeState(AIState.Investigate);
                    }
                    break;

                case TacticalOrderType.Defend:
                case TacticalOrderType.Regroup:
                case TacticalOrderType.Patrol:
                    // Store position for AI to consider, but don't execute movement directly
                    if (order.targetPosition != Vector3.zero)
                    {
                        lastKnownEnemyPosition = order.targetPosition;
                        ChangeState(AIState.Investigate);
                    }
                    break;

                case TacticalOrderType.Retreat:
                    // Retreat logic handled by state machine
                    ChangeState(AIState.Retreat);
                    break;
            }
        }

        /// <summary>
        /// Get closest target from a list
        /// </summary>
        private Unit GetClosestTarget(List<Unit> targets)
        {
            Unit closest = null;
            float closestDistance = float.MaxValue;

            foreach (Unit target in targets)
            {
                if (target == null || target.IsDead) continue;

                float distance = Vector3.Distance(transform.position, target.transform.position);
                if (distance < closestDistance)
                {
                    closestDistance = distance;
                    closest = target;
                }
            }

            return closest;
        }

        /// <summary>
        /// Apply result from multi-threaded AI job
        /// </summary>
        public void ApplyJobResult(AIDecisionResult result)
        {
            // Convert job result to AI state
            AIState newState = (AIState)result.newState;

            // Apply state change if different
            if (newState != currentState)
            {
                ChangeState(newState);
                lastActionTime = Time.time; // Track when this unit took action
            }

            // DO NOT APPLY MOVEMENT COMMANDS FROM JOB SYSTEM
            // AI makes its own movement decisions in UpdateEngageState()
            if ((result.moveTarget.x != 0 || result.moveTarget.z != 0))
            {
                if (enableDetailedLogging)
                {
                    Vector3 moveTarget = new Vector3(result.moveTarget.x, result.moveTarget.y, result.moveTarget.z);
                    Debug.Log($"AI {gameObject.name}: Job system suggested movement to {moveTarget}, but AI makes own movement decisions");
                }
                lastActionTime = Time.time; // Track when this unit took action
            }

            // Apply attack commands
            if (result.hasAttackTarget)
            {
                Vector3 attackTarget = new Vector3(result.attackTarget.x, result.attackTarget.y, result.attackTarget.z);

                // Find the actual enemy unit at this position (approximate)
                Unit targetUnit = null;
                float closestDistance = 5f; // 5m tolerance

                foreach (Unit enemy in unit.DetectedEnemies)
                {
                    if (enemy != null)
                    {
                        float distance = Vector3.Distance(enemy.transform.position, attackTarget);
                        if (distance < closestDistance)
                        {
                            closestDistance = distance;
                            targetUnit = enemy;
                        }
                    }
                }

                if (targetUnit != null)
                {
                    SetPrimaryTarget(targetUnit);
                    unit.SetTarget(targetUnit);
                }
            }
        }

        // Public properties for job system access
        public float SuppressionLevel => suppressionLevel;
        public float MoraleLevel => moraleLevel;
        public float LastActionTime => lastActionTime;

        /// <summary>
        /// Determine tactical state based on enemy analysis and unit capabilities
        /// </summary>
        private AIState DetermineTacticalState(Unit enemy, float distance)
        {
            if (enemy == null) return currentState;

            // Analyze tactical situation
            bool hasAdvantage = AnalyzeTacticalAdvantage(enemy);
            bool hasSupport = nearbyAllies.Count > 0;
            bool enemyIsWounded = enemy.CurrentHealth < enemy.MaxHealth * 0.7f;

            // Decision tree based on tactical analysis
            if (distance <= optimalFireRange)
            {
                if (hasAdvantage || enemyIsWounded)
                {
                    return AIState.Engage;
                }
                else if (!hasSupport && suppressionLevel > 30f)
                {
                    return AIState.Retreat;
                }
                else
                {
                    return AIState.Engage;
                }
            }
            else if (distance <= pursuitRange)
            {
                if (hasAdvantage && hasSupport)
                {
                    return AIState.Assault;
                }
                else
                {
                    return AIState.Engage;
                }
            }
            else
            {
                return AIState.Investigate;
            }
        }

        /// <summary>
        /// Analyze tactical advantage against an enemy
        /// </summary>
        private bool AnalyzeTacticalAdvantage(Unit enemy)
        {
            if (enemy == null) return false;

            // Health advantage
            float ourHealthRatio = (float)unit.CurrentHealth / unit.MaxHealth;
            float enemyHealthRatio = (float)enemy.CurrentHealth / enemy.MaxHealth;
            bool healthAdvantage = ourHealthRatio > enemyHealthRatio + 0.2f;

            // Numerical advantage
            bool numericalAdvantage = nearbyAllies.Count > 0;

            // Position advantage (higher ground, cover, etc.)
            bool positionAdvantage = transform.position.y > enemy.transform.position.y + 2f;

            // Weapon range advantage
            bool rangeAdvantage = unit.WeaponRange > enemy.WeaponRange;

            // Return true if we have multiple advantages
            int advantages = 0;
            if (healthAdvantage) advantages++;
            if (numericalAdvantage) advantages++;
            if (positionAdvantage) advantages++;
            if (rangeAdvantage) advantages++;

            return advantages >= 2;
        }

        /// <summary>
        /// Change to a new AI state
        /// </summary>
        private void ChangeState(AIState newState)
        {
            if (newState == currentState) return;

            // Exit current state
            ExitState(currentState);

            // Store previous state
            previousState = currentState;
            currentState = newState;
            stateEnterTime = Time.time;

            // Enter new state
            EnterState(newState);

            // Trigger state change event
            OnStateChanged?.Invoke(this, previousState, currentState);

            // Debug logging
            if (enableDetailedLogging)
            {
                DebugManager.Instance?.LogAI($"{unit.name}: {previousState} → {newState}");
            }
        }

        private void EnterState(AIState state)
        {
            switch (state)
            {
                case AIState.Idle:
                    unit.StopMovement();
                    break;

                case AIState.Patrol:
                    hasCalledForReinforcements = false;
                    if (patrolPoints != null && patrolPoints.Length > 0)
                    {
                        MoveToNextPatrolPoint();
                    }
                    // TacticalAwareness handles all intelligent movement decisions
                    break;

                case AIState.Investigate:
                    // Set target if we have detected enemies
                    if (unit.DetectedEnemies.Count > 0)
                    {
                        Unit closestEnemy = unit.GetClosestEnemy();
                        if (closestEnemy != null && closestEnemy != unit && closestEnemy.Faction != unit.Faction)
                        {
                            SetPrimaryTarget(closestEnemy);
                            unit.SetTarget(closestEnemy);
                        }
                    }

                    if (lastKnownEnemyPosition != Vector3.zero)
                    {
                        unit.MoveTo(lastKnownEnemyPosition);
                    }
                    break;

                case AIState.Engage:
                    // Set primary target and ensure Unit's combat system also targets it
                    if (primaryTarget == null)
                    {
                        Unit target = unit.GetClosestEnemy();
                        if (target != null && target != unit && target.Faction != unit.Faction)
                        {
                            SetPrimaryTarget(target);
                            // Also set the Unit's combat target
                            unit.SetTarget(target);
                        }
                        else
                        {
                            ChangeState(AIState.Patrol);
                            return;
                        }
                    }
                    else
                    {
                        // Ensure Unit's combat system is targeting the same enemy
                        if (primaryTarget != unit && primaryTarget.Faction != unit.Faction)
                        {
                            unit.SetTarget(primaryTarget);
                        }
                    }

                    OnEnemyEngaged?.Invoke(this, primaryTarget);

                    // Call for reinforcements if enabled
                    if (useGroupBehavior && !hasCalledForReinforcements)
                    {
                        CallForReinforcements();
                    }
                    break;

                case AIState.Retreat:
                    OnRetreatStarted?.Invoke(this);
                    Vector3 retreatPosition = CalculateRetreatPosition();
                    unit.MoveTo(retreatPosition);
                    break;
            }
        }

        private void ExitState(AIState state)
        {
            switch (state)
            {
                case AIState.Engage:
                    SetPrimaryTarget(null);
                    break;
            }
        }

        /// <summary>
        /// Apply suppression to this AI unit
        /// </summary>
        public void ApplySuppression(float amount)
        {
            suppressionLevel = Mathf.Min(maxSuppressionLevel, suppressionLevel + amount);

            // Reduce morale when suppressed
            moraleLevel = Mathf.Max(0f, moraleLevel - amount * 0.5f);

            if (DebugManager.Instance != null)
            {
                DebugManager.Instance.LogAISuppression(this, suppressionLevel, moraleLevel);
            }
        }

        /// <summary>
        /// Set primary target for engagement - works WITH Unit's auto-engagement
        /// </summary>
        private void SetPrimaryTarget(Unit target)
        {
            // CRITICAL: Prevent self-targeting
            if (target == unit)
            {
                Debug.LogError($"AI {unit.name}: Attempted to target self! Ignoring.");
                return;
            }

            // CRITICAL: Only target enemies
            if (target != null && target.Faction == unit.Faction)
            {
                Debug.LogError($"AI {unit.name}: Attempted to target friendly unit {target.name}! Ignoring.");
                return;
            }

            primaryTarget = target;

            if (target != null)
            {
                lastKnownEnemyPosition = target.transform.position;
                lastEnemyContactTime = Time.time;
            }
        }

        /// <summary>
        /// Get closest enemy within specified range
        /// </summary>
        private Unit GetClosestEnemyInRange(float range)
        {
            Unit closestEnemy = null;
            float closestDistance = range;

            foreach (Unit enemy in unit.DetectedEnemies)
            {
                if (enemy == null || enemy.IsDead) continue;

                float distance = Vector3.Distance(transform.position, enemy.transform.position);
                if (distance < closestDistance)
                {
                    closestDistance = distance;
                    closestEnemy = enemy;
                }
            }

            return closestEnemy;
        }

        /// <summary>
        /// Update suppression and morale levels
        /// </summary>
        private void UpdateSuppressionAndMorale()
        {
            // Decay suppression over time
            if (suppressionLevel > 0f)
            {
                suppressionLevel = Mathf.Max(0f, suppressionLevel - suppressionDecayRate * Time.deltaTime);
            }

            // Recover morale when not suppressed
            if (suppressionLevel < 20f && moraleLevel < maxMoraleLevel)
            {
                moraleLevel = Mathf.Min(maxMoraleLevel, moraleLevel + moraleRecoveryRate * Time.deltaTime);
            }
        }

        /// <summary>
        /// Update current state behavior
        /// </summary>
        private void UpdateCurrentState()
        {
            // No tactical decision blocking - AI is now the single decision maker

            switch (currentState)
            {
                case AIState.Engage:
                    UpdateEngageState();
                    break;
                case AIState.Patrol:
                    UpdatePatrolState();
                    break;
                case AIState.Investigate:
                    UpdateInvestigateState();
                    break;
                case AIState.Retreat:
                    UpdateRetreatState();
                    break;
            }
        }

        /// <summary>
        /// Update engage state - AI executes intelligent engagement using awareness systems
        /// </summary>
        private void UpdateEngageState()
        {
            // Check if target is still valid
            if (primaryTarget == null || primaryTarget.IsDead)
            {
                // Find new target using vision system
                Unit newTarget = unit.GetClosestEnemy();
                if (newTarget != null && newTarget != unit && newTarget.Faction != unit.Faction)
                {
                    SetPrimaryTarget(newTarget);
                    // Ensure Unit's combat system targets the same enemy
                    unit.SetTarget(newTarget);
                }
                else
                {
                    // No targets available - return to patrol
                    ChangeState(AIState.Patrol);
                    return;
                }
            }
            else
            {
                // Ensure Unit's combat system is targeting the same enemy
                if (primaryTarget != unit && primaryTarget.Faction != unit.Faction)
                {
                    unit.SetTarget(primaryTarget);
                }
            }

            // AI IS THE SINGLE DECISION MAKER - Make movement decisions based on combat needs
            if (primaryTarget != null)
            {
                float distanceToTarget = Vector3.Distance(transform.position, primaryTarget.transform.position);
                float optimalRange = GetPreferredEngagementRange();
                float minRange = unit.WeaponRange * 0.3f; // Minimum safe distance
                float maxRange = unit.WeaponRange * 0.9f; // Maximum effective range

                float timeSinceStateChange = Time.time - stateEnterTime;
                bool canTakeAction = timeSinceStateChange > combatUpdateOffset;

                // SINGLE DECISION TREE - no conflicting conditions
                if (!canTakeAction)
                {
                    // Wait for action cooldown
                    return;
                }
                else if (distanceToTarget > maxRange)
                {
                    // Too far - move closer to effective range
                    Vector3 directionToTarget = (primaryTarget.transform.position - transform.position).normalized;
                    Vector3 movePosition = primaryTarget.transform.position - directionToTarget * optimalRange;
                    unit.MoveTo(movePosition);
                }
                else if (distanceToTarget < minRange)
                {
                    // Too close - back away to safe distance
                    Vector3 retreatDirection = (transform.position - primaryTarget.transform.position).normalized;
                    Vector3 retreatPosition = primaryTarget.transform.position + retreatDirection * optimalRange;
                    unit.MoveTo(retreatPosition);
                }
                else
                {
                    // Within effective range - STOP and let combat system handle firing
                    unit.StopMovement();
                }
            }
        }

        // All positioning calculation methods removed - TacticalAwareness handles ALL tactical positioning

        /// <summary>
        /// Update patrol state - AI executes intelligent patrol using awareness systems
        /// </summary>
        private void UpdatePatrolState()
        {
            // MOVEMENT DECISIONS: Only make movement decisions if NOT using job system
            if (!useJobSystem || !registeredWithJobSystem)
            {
                // Traditional AI patrol logic
                if (patrolPoints != null && patrolPoints.Length > 0)
                {
                    // Check if we've reached current patrol point or need to start moving
                    if (!unit.IsMoving || Vector3.Distance(transform.position, patrolPoints[currentPatrolIndex].position) <= 2f)
                {
                    // Wait between patrol moves for awareness scanning
                    if (Time.time - lastPatrolMoveTime >= patrolMoveInterval)
                    {
                        MoveToNextPatrolPoint();
                        lastPatrolMoveTime = Time.time;
                    }
                }
            }
            // Use random patrol if configured
            else if (randomPatrol)
            {
                // Check if we need to move to a new random position
                if (!unit.IsMoving || Vector3.Distance(transform.position, randomPatrolCenter) <= 2f)
                {
                    if (Time.time - lastPatrolMoveTime >= patrolMoveInterval)
                    {
                        StartRandomPatrol();
                        lastPatrolMoveTime = Time.time;
                    }
                }
            }
                // No patrol configuration - hold position but stay alert
                else
                {
                    if (unit.IsMoving)
                    {
                        unit.StopMovement();
                    }
                }
            }
            // If using job system, patrol movement is handled by jobs
        }

        /// <summary>
        /// Move to next patrol point
        /// </summary>
        private void MoveToNextPatrolPoint()
        {
            if (patrolPoints == null || patrolPoints.Length == 0) return;

            currentPatrolIndex = (currentPatrolIndex + 1) % patrolPoints.Length;
            if (patrolPoints[currentPatrolIndex] != null)
            {
                unit.MoveTo(patrolPoints[currentPatrolIndex].position);
            }
        }

        /// <summary>
        /// Start random patrol movement
        /// </summary>
        private void StartRandomPatrol()
        {
            // Generate random position within patrol radius
            Vector2 randomCircle = Random.insideUnitCircle * randomPatrolRadius;
            Vector3 randomPosition = randomPatrolCenter + new Vector3(randomCircle.x, 0, randomCircle.y);

            // Ensure the position is on the NavMesh
            if (UnityEngine.AI.NavMesh.SamplePosition(randomPosition, out UnityEngine.AI.NavMeshHit hit, randomPatrolRadius, UnityEngine.AI.NavMesh.AllAreas))
            {
                unit.MoveTo(hit.position);
            }
            else
            {
                // NavMesh constraint: move towards home position
                unit.MoveTo(homePosition);
            }
        }

        /// <summary>
        /// Update investigate state
        /// </summary>
        private void UpdateInvestigateState()
        {
            // AI is the single decision maker - no blocking needed

            // Check if we have enemies in range to engage
            if (unit.DetectedEnemies.Count > 0)
            {
                Unit closestEnemy = unit.GetClosestEnemy();
                if (closestEnemy != null && closestEnemy != unit && closestEnemy.Faction != unit.Faction)
                {
                    float distance = Vector3.Distance(transform.position, closestEnemy.transform.position);

                    // If enemy is detected, set target and engage immediately
                    // Let the Engage state handle movement decisions
                    SetPrimaryTarget(closestEnemy);
                    unit.SetTarget(closestEnemy);
                    ChangeState(AIState.Engage);
                    return;
                }
            }

            // No enemies detected - check if we've reached investigation point
            if (lastKnownEnemyPosition != Vector3.zero)
            {
                float distanceToInvestigationPoint = Vector3.Distance(transform.position, lastKnownEnemyPosition);

                // If we're not at the investigation point and not moving, move there
                if (distanceToInvestigationPoint > 2f && !unit.IsMoving)
                {
                    unit.MoveTo(lastKnownEnemyPosition);
                }
                // If we've reached the investigation point, clear it and return to patrol
                else if (distanceToInvestigationPoint <= 2f)
                {
                    lastKnownEnemyPosition = Vector3.zero;
                    ChangeState(AIState.Patrol);
                }
            }
            else
            {
                // No investigation point - return to patrol
                ChangeState(AIState.Patrol);
            }
        }

        /// <summary>
        /// Update retreat state
        /// </summary>
        private void UpdateRetreatState()
        {
            if (!unit.IsMoving)
            {
                // Check if we can return to combat
                float healthPercentage = (float)unit.CurrentHealth / unit.MaxHealth;
                if (healthPercentage > retreatHealthThreshold + 0.1f && suppressionLevel < 30f)
                {
                    ChangeState(AIState.Patrol);
                }
            }
        }

        /// <summary>
        /// Calculate retreat position away from threats
        /// </summary>
        private Vector3 CalculateRetreatPosition()
        {
            Vector3 retreatDirection = Vector3.zero;
            int threatCount = 0;

            // Move away from all known enemies
            foreach (Unit enemy in unit.DetectedEnemies)
            {
                if (enemy != null && !enemy.IsDead)
                {
                    Vector3 awayFromEnemy = transform.position - enemy.transform.position;
                    retreatDirection += awayFromEnemy.normalized;
                    threatCount++;
                }
            }

            if (threatCount > 0)
            {
                retreatDirection /= threatCount;
            }
            else
            {
                // No specific threats - retreat towards home
                retreatDirection = (homePosition - transform.position).normalized;
            }

            return transform.position + retreatDirection * 20f;
        }

        /// <summary>
        /// Update group coordination
        /// </summary>
        private void UpdateGroupCoordination()
        {
            if (!useGroupBehavior) return;

            // Find nearby allies
            nearbyAllies.Clear();
            Collider[] nearbyColliders = Physics.OverlapSphere(transform.position, unit.CommunicationRange);

            foreach (Collider col in nearbyColliders)
            {
                AI allyAI = col.GetComponent<AI>();
                if (allyAI != null && allyAI != this && allyAI.unit.Faction == unit.Faction)
                {
                    nearbyAllies.Add(allyAI);
                }
            }
        }

        /// <summary>
        /// Call for reinforcements
        /// </summary>
        private void CallForReinforcements()
        {
            if (hasCalledForReinforcements) return;

            hasCalledForReinforcements = true;
            LastReinforcementCall = Time.time;

            // Notify command hierarchy
            if (CommandHierarchy.Instance != null)
            {
                CommandHierarchy.Instance.RequestReinforcements(unit, transform.position, reinforcementCallRange);
            }
        }

        // Old event-driven OnTacticalDecisionMade method removed
        // AI now actively reads from TacticalAwareness as a perception system

        /// <summary>
        /// Event handlers
        /// </summary>
        private void OnEnemyDetected(Unit detector, Unit enemy)
        {
            if (detector != unit) return;

            if (!knownEnemies.Contains(enemy))
            {
                knownEnemies.Add(enemy);
            }

            lastEnemyContactTime = Time.time;
            lastKnownEnemyPosition = enemy.transform.position;

            // TacticalAwareness handles ALL engagement decisions
            // AI is pure executor - only tracks enemy data for TacticalAwareness to use
        }

        private void OnEnemyLost(Unit detector, Unit enemy)
        {
            if (detector != unit) return;

            knownEnemies.Remove(enemy);
        }

        private void OnHealthChanged(Unit changedUnit, int newHealth)
        {
            if (changedUnit != unit) return;

            // Check if we need to retreat due to low health
            float healthPercentage = (float)newHealth / unit.MaxHealth;
            if (healthPercentage <= retreatHealthThreshold && currentState != AIState.Retreat)
            {
                ChangeState(AIState.Retreat);
            }
        }

        /// <summary>
        /// Investigate a specific position
        /// </summary>
        public void InvestigatePosition(Vector3 position)
        {
            lastKnownEnemyPosition = position;
            ChangeState(AIState.Investigate);
            unit.MoveTo(position);
        }

        /// <summary>
        /// Engage a specific target
        /// </summary>
        public void EngageTarget(Unit target)
        {
            if (target != null)
            {
                SetPrimaryTarget(target);
                ChangeState(AIState.Engage);
            }
        }

        /// <summary>
        /// Respond to reinforcement call
        /// </summary>
        public void RespondToReinforcement(Vector3 position, Unit targetUnit = null)
        {
            // Move towards reinforcement position
            unit.MoveTo(position);

            // If a specific unit needs support, set it as target
            if (targetUnit != null)
            {
                SetPrimaryTarget(targetUnit);
                ChangeState(AIState.Engage);
            }
            else
            {
                ChangeState(AIState.Support);
            }
        }

        #endregion
    }
}
