using UnityEngine;
using System.Collections.Generic;

namespace ColdVor.RTS
{
    /// <summary>
    /// Central game manager that coordinates all RTS systems
    /// Ensures proper initialization order and system integration
    /// </summary>
    public class RTSGameManager : SingletonBase<RTSGameManager>
    {
        [Header("System References")]
        [SerializeField] private FactionManager factionManager;
        [SerializeField] private CombatSystem combatSystem;
        [SerializeField] private IntelligenceNetwork intelligenceNetwork;
        [SerializeField] private CommandHierarchy commandHierarchy;
        [SerializeField] private TacticalAwareness tacticalAwareness;

        [Header("Game Settings")]
        [SerializeField] private bool autoInitializeSystems = true;
        [SerializeField] private bool enableDebugMode = false;
        [SerializeField] private float systemUpdateInterval = 1f;

        [Header("Unit Spawning")]
        [SerializeField] private GameObject[] playerUnitPrefabs;
        [SerializeField] private GameObject[] enemyUnitPrefabs;
        [SerializeField] private Transform[] playerSpawnPoints;
        [SerializeField] private Transform[] enemySpawnPoints;

        // System status tracking
        private Dictionary<string, bool> systemStatus = new Dictionary<string, bool>();
        private float lastSystemUpdate;
        private bool allSystemsInitialized = false;

        // Events
        public System.Action OnAllSystemsInitialized;
        public System.Action<string, bool> OnSystemStatusChanged;

        protected override void OnSingletonAwake()
        {
            if (autoInitializeSystems)
            {
                InitializeAllSystems();
            }
        }

        private void Start()
        {
            if (autoInitializeSystems)
            {
                StartCoroutine(WaitForSystemInitialization());
            }
        }

        private void Update()
        {
            if (allSystemsInitialized && Time.time - lastSystemUpdate >= systemUpdateInterval)
            {
                UpdateSystemStatus();
                lastSystemUpdate = Time.time;
            }

            if (enableDebugMode)
            {
                HandleDebugInput();
            }
        }

        #region System Initialization

        private void InitializeAllSystems()
        {
            Debug.Log("RTSGameManager: Initializing all systems...");

            // Initialize systems in proper order
            InitializeFactionManager();
            InitializeCombatSystem();
            InitializeIntelligenceNetwork();
            InitializeCommandHierarchy();
            InitializeTacticalAwareness();

            // Subscribe to system events
            SubscribeToSystemEvents();
        }

        private void InitializeFactionManager()
        {
            // Ensure FactionManager singleton exists
            if (FactionManager.Instance == null)
            {
                GameObject fmGO = new GameObject("FactionManager");
                fmGO.AddComponent<FactionManager>();
            }
            factionManager = FactionManager.Instance;
            systemStatus["FactionManager"] = factionManager != null;
            Debug.Log("FactionManager initialized");
        }

        private void InitializeCombatSystem()
        {
            // Ensure CombatSystem singleton exists
            if (CombatSystem.Instance == null)
            {
                GameObject csGO = new GameObject("CombatSystem");
                csGO.AddComponent<CombatSystem>();
            }
            combatSystem = CombatSystem.Instance;
            systemStatus["CombatSystem"] = combatSystem != null;
            Debug.Log("CombatSystem initialized");
        }

        private void InitializeIntelligenceNetwork()
        {
            // Ensure IntelligenceNetwork singleton exists
            if (IntelligenceNetwork.Instance == null)
            {
                GameObject inGO = new GameObject("IntelligenceNetwork");
                inGO.AddComponent<IntelligenceNetwork>();
            }
            intelligenceNetwork = IntelligenceNetwork.Instance;
            systemStatus["IntelligenceNetwork"] = intelligenceNetwork != null;
            Debug.Log("IntelligenceNetwork initialized");
        }

        private void InitializeCommandHierarchy()
        {
            // Ensure CommandHierarchy singleton exists
            if (CommandHierarchy.Instance == null)
            {
                GameObject chGO = new GameObject("CommandHierarchy");
                chGO.AddComponent<CommandHierarchy>();
            }
            commandHierarchy = CommandHierarchy.Instance;
            systemStatus["CommandHierarchy"] = commandHierarchy != null;
            Debug.Log("CommandHierarchy initialized");
        }

        private void InitializeTacticalAwareness()
        {
            // Ensure TacticalAwareness singleton exists
            if (TacticalAwareness.Instance == null)
            {
                GameObject taGO = new GameObject("TacticalAwareness");
                taGO.AddComponent<TacticalAwareness>();
            }
            tacticalAwareness = TacticalAwareness.Instance;
            systemStatus["TacticalAwareness"] = tacticalAwareness != null;
            Debug.Log("TacticalAwareness initialized");
        }

        private System.Collections.IEnumerator WaitForSystemInitialization()
        {
            // Wait a frame for all systems to initialize
            yield return new WaitForEndOfFrame();
            
            // Check if all systems are ready
            bool allReady = true;
            foreach (var status in systemStatus.Values)
            {
                if (!status)
                {
                    allReady = false;
                    break;
                }
            }

            if (allReady)
            {
                allSystemsInitialized = true;
                OnAllSystemsInitialized?.Invoke();
                if (DebugManager.Instance != null)
                {
                    DebugManager.Instance.LogSystem("RTSGameManager", "All RTS systems initialized successfully!");
                }

                // Spawn initial units if configured
                if (playerSpawnPoints.Length > 0 && playerUnitPrefabs.Length > 0)
                {
                    SpawnInitialUnits();
                }
            }
            else
            {
                if (DebugManager.Instance != null)
                {
                    DebugManager.Instance.LogWarning("RTSGameManager", "Some RTS systems failed to initialize properly");
                }
            }
        }

        #endregion

        #region System Integration

        private void SubscribeToSystemEvents()
        {
            // Subscribe to key system events for coordination
            if (intelligenceNetwork != null)
            {
                intelligenceNetwork.OnIntelReportCreated += OnIntelReportCreated;
                intelligenceNetwork.OnThreatLevelChanged += OnThreatLevelChanged;
            }

            if (commandHierarchy != null)
            {
                commandHierarchy.OnTacticalOrderIssued += OnTacticalOrderIssued;
            }

            if (tacticalAwareness != null)
            {
                tacticalAwareness.OnTacticalDecisionMade += OnTacticalDecisionMade;
            }

            if (combatSystem != null)
            {
                combatSystem.OnUnitKilled += OnUnitKilled;
            }
        }

        private void OnIntelReportCreated(IntelReport report)
        {
            if (enableDebugMode)
            {
                Debug.Log($"Intel Report: {report.intelType} - {report.subjectFaction} unit spotted by {report.reporterFaction}");
            }
        }

        private void OnThreatLevelChanged(Faction faction, ThreatLevel newLevel)
        {
            if (enableDebugMode)
            {
                Debug.Log($"Threat Level Changed: {faction} now at {newLevel}");
            }

            // Could trigger UI updates or other responses here
        }

        private void OnTacticalOrderIssued(TacticalOrder order)
        {
            if (enableDebugMode)
            {
                Debug.Log($"Tactical Order: {order.commander.name} issued {order.orderType} to {order.assignedUnits.Count} units");
            }
        }

        private void OnTacticalDecisionMade(TacticalDecision decision)
        {
            if (enableDebugMode)
            {
                Debug.Log($"Tactical Decision: {decision.unit.name} decided to {decision.decisionType} (confidence: {decision.confidence:F2})");
            }
        }

        private void OnUnitKilled(Unit killedUnit)
        {
            if (enableDebugMode)
            {
                Debug.Log($"Unit Killed: {killedUnit.name} ({killedUnit.Faction})");
            }

            // Refresh systems that track units
            if (tacticalAwareness != null)
            {
                tacticalAwareness.ForceAssessment(null); // This will trigger a refresh
            }
        }

        #endregion

        #region Unit Management

        private void SpawnInitialUnits()
        {
            // Spawn player units
            for (int i = 0; i < playerSpawnPoints.Length && i < playerUnitPrefabs.Length; i++)
            {
                if (playerSpawnPoints[i] != null && playerUnitPrefabs[i] != null)
                {
                    GameObject playerUnit = Instantiate(playerUnitPrefabs[i], playerSpawnPoints[i].position, playerSpawnPoints[i].rotation);
                    Unit unit = playerUnit.GetComponent<Unit>();
                    if (unit != null)
                    {
                        // Ensure player units are set to player faction
                        SetUnitFaction(unit, Faction.Player);
                    }
                }
            }

            // Spawn enemy units
            for (int i = 0; i < enemySpawnPoints.Length && i < enemyUnitPrefabs.Length; i++)
            {
                if (enemySpawnPoints[i] != null && enemyUnitPrefabs[i] != null)
                {
                    GameObject enemyUnit = Instantiate(enemyUnitPrefabs[i], enemySpawnPoints[i].position, enemySpawnPoints[i].rotation);
                    Unit unit = enemyUnit.GetComponent<Unit>();
                    if (unit != null)
                    {
                        // Ensure enemy units are set to enemy faction and have AI
                        SetUnitFaction(unit, Faction.Enemy);
                        
                        // Add AI component if not present
                        if (unit.GetComponent<AI>() == null)
                        {
                            unit.gameObject.AddComponent<AI>();
                        }
                    }
                }
            }

            Debug.Log($"Spawned {playerSpawnPoints.Length} player units and {enemySpawnPoints.Length} enemy units");
        }

        private void SetUnitFaction(Unit unit, Faction faction)
        {
            // Use the proper public method instead of reflection
            unit.SetFaction(faction);
        }

        #endregion

        #region System Monitoring

        private void UpdateSystemStatus()
        {
            // Check if all systems are still functioning
            CheckSystemHealth("FactionManager", FactionManager.Instance != null);
            CheckSystemHealth("CombatSystem", CombatSystem.Instance != null);
            CheckSystemHealth("IntelligenceNetwork", IntelligenceNetwork.Instance != null);
            CheckSystemHealth("CommandHierarchy", CommandHierarchy.Instance != null);
            CheckSystemHealth("TacticalAwareness", TacticalAwareness.Instance != null);

            // Check unit-level systems health
            CheckUnitSystemsHealth();
        }

        private void CheckUnitSystemsHealth()
        {
            Unit[] allUnits = FindObjectsByType<Unit>(FindObjectsSortMode.None);
            int totalUnits = allUnits.Length;
            int unitsWithMovement = 0;
            int unitsWithHealth = 0;
            int unitsWithWeapons = 0;
            int unitsWithVision = 0;

            foreach (Unit unit in allUnits)
            {
                if (unit.MovementSystem != null) unitsWithMovement++;
                if (unit.HealthSystem != null) unitsWithHealth++;
                if (unit.WeaponSystem != null) unitsWithWeapons++;
                if (unit.VisionSystem != null) unitsWithVision++;
            }

            // Log system coverage
            if (DebugManager.Instance != null && totalUnits > 0)
            {
                DebugManager.Instance.LogSystem("RTSGameManager",
                    $"Unit Systems Coverage: Movement {unitsWithMovement}/{totalUnits}, " +
                    $"Health {unitsWithHealth}/{totalUnits}, " +
                    $"Weapons {unitsWithWeapons}/{totalUnits}, " +
                    $"Vision {unitsWithVision}/{totalUnits}");
            }
        }

        private void CheckSystemHealth(string systemName, bool isHealthy)
        {
            if (systemStatus.ContainsKey(systemName))
            {
                if (systemStatus[systemName] != isHealthy)
                {
                    systemStatus[systemName] = isHealthy;
                    OnSystemStatusChanged?.Invoke(systemName, isHealthy);
                    
                    if (!isHealthy)
                    {
                        Debug.LogError($"System {systemName} has failed!");
                    }
                    else
                    {
                        Debug.Log($"System {systemName} has recovered");
                    }
                }
            }
        }

        #endregion

        #region Debug Functions

        private void HandleDebugInput()
        {
            if (Input.GetKeyDown(KeyCode.F1))
            {
                DebugPrintSystemStatus();
            }

            if (Input.GetKeyDown(KeyCode.F2))
            {
                DebugSpawnEnemyUnit();
            }

            if (Input.GetKeyDown(KeyCode.F3))
            {
                DebugTriggerTacticalAssessment();
            }
        }

        private void DebugPrintSystemStatus()
        {
            Debug.Log("=== RTS System Status ===");
            foreach (var kvp in systemStatus)
            {
                Debug.Log($"{kvp.Key}: {(kvp.Value ? "OK" : "FAILED")}");
            }

            if (intelligenceNetwork != null)
            {
                var playerIntel = intelligenceNetwork.GetFactionIntelligence(Faction.Player);
                Debug.Log($"Player Intelligence Reports: {playerIntel.Count}");
            }
        }

        private void DebugSpawnEnemyUnit()
        {
            if (enemyUnitPrefabs.Length > 0)
            {
                Vector3 spawnPos = Camera.main.transform.position + Camera.main.transform.forward * 10f;
                GameObject enemyUnit = Instantiate(enemyUnitPrefabs[0], spawnPos, Quaternion.identity);
                Unit unit = enemyUnit.GetComponent<Unit>();
                if (unit != null)
                {
                    SetUnitFaction(unit, Faction.Enemy);
                    enemyUnit.AddComponent<AI>();
                }
                Debug.Log("Debug: Spawned enemy unit");
            }
        }

        private void DebugTriggerTacticalAssessment()
        {
            if (tacticalAwareness != null)
            {
                Unit[] allUnits = FindObjectsByType<Unit>(FindObjectsSortMode.None);
                foreach (Unit unit in allUnits)
                {
                    tacticalAwareness.ForceAssessment(unit);
                }
                Debug.Log("Debug: Triggered tactical assessment for all units");
            }
        }

        #endregion

        #region Public Interface

        /// <summary>
        /// Check if all systems are initialized and ready
        /// </summary>
        public bool AreAllSystemsReady()
        {
            return allSystemsInitialized;
        }

        /// <summary>
        /// Get the status of a specific system
        /// </summary>
        public bool GetSystemStatus(string systemName)
        {
            return systemStatus.ContainsKey(systemName) ? systemStatus[systemName] : false;
        }

        /// <summary>
        /// Manually refresh all systems (useful after spawning/destroying units)
        /// </summary>
        public void RefreshAllSystems()
        {
            if (tacticalAwareness != null) tacticalAwareness.ForceAssessment(null);
            // Add other system refresh calls as needed
        }

        #endregion
    }
}
