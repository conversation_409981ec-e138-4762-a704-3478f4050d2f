using System.Collections.Generic;
using UnityEngine;

namespace ColdVor.RTS
{
    /// <summary>
    /// Central command system that coordinates unit selection, movement, and actions
    /// Integrates with all other RTS systems to provide unified command interface
    /// </summary>
    public class UnitCommandSystem : MonoBehaviour
    {
        [Header("Command Settings")]
        [SerializeField] private LayerMask unitLayerMask = 3;
        [SerializeField] private bool enableFormationCommands = true;
        [SerializeField] private bool enableWaypointCommands = true;



        [Header("Visual Feedback")]
        [SerializeField] private GameObject moveCommandIndicator;
        [SerializeField] private Color moveCommandColor = Color.green;
        [SerializeField] private Color attackCommandColor = Color.red;
        [SerializeField] private float commandIndicatorDuration = 2f;

        // System references
        private PathfindingManager pathfindingManager;
        private WaypointManager waypointManager;
        private Camera playerCamera;

        // Command state
        private CommandMode currentCommandMode = CommandMode.Move;
        private FormationType currentFormation = FormationType.Auto;



        // Events
        public System.Action<CommandType, Vector3> OnCommandIssued;
        public System.Action<CommandMode> OnCommandModeChanged;

        private void Awake()
        {
            playerCamera = Camera.main;
            if (playerCamera == null)
                playerCamera = FindFirstObjectByType<Camera>();
        }

        private void Start()
        {
            pathfindingManager = FindFirstObjectByType<PathfindingManager>();
            waypointManager = FindFirstObjectByType<WaypointManager>();

            // Subscribe to centralized input events
            if (RTSInputManager.Instance != null)
            {
                RTSInputManager.Instance.OnRightClickCommand += HandleRightClickCommand;
                RTSInputManager.Instance.OnStopCommand += IssueStopCommand;
                RTSInputManager.Instance.OnHoldPositionCommand += IssueHoldPositionCommand;
                RTSInputManager.Instance.OnFormationToggleCommand += CycleFormationType;
            }
        }



        private void HandleRightClickCommand(Vector3 targetPosition)
        {
            if (RTSInputManager.Instance != null && RTSInputManager.Instance.HasSelection)
            {
                IssueCommand(targetPosition);
            }
        }



        private void IssueCommand(Vector3 targetPosition)
        {
            if (RTSInputManager.Instance == null || !RTSInputManager.Instance.HasSelection) return;

            var selectedUnits = RTSInputManager.Instance.SelectedUnits;
            bool isQueueCommand = RTSInputManager.Instance.IsKeyHeld(KeyCode.LeftShift) || RTSInputManager.Instance.IsKeyHeld(KeyCode.RightShift);
            bool isPatrolCommand = RTSInputManager.Instance.IsKeyHeld(RTSInputManager.Instance.PatrolModifierKey);

            // Determine command type based on modifiers and what we're clicking on
            CommandType commandType = DetermineCommandType(isPatrolCommand);

            switch (commandType)
            {
                case CommandType.Move:
                    IssueMoveCommand(selectedUnits, targetPosition, isQueueCommand);
                    break;
                case CommandType.Attack:
                    IssueAttackCommand(selectedUnits, targetPosition, isQueueCommand);
                    break;
                case CommandType.Follow:
                    // Follow command removed - not supported
                    break;
                case CommandType.Patrol:
                    // Patrol is now handled by WaypointManager modal system
                    break;
            }

            // Show visual feedback
            ShowCommandIndicator(targetPosition, commandType);

            // Trigger event
            OnCommandIssued?.Invoke(commandType, targetPosition);
        }

        private CommandType DetermineCommandType(bool isPatrolCommand)
        {
            // If patrol modifier is held, return patrol command
            if (isPatrolCommand)
            {
                return CommandType.Patrol;
            }

            Ray ray = playerCamera.ScreenPointToRay(RTSInputManager.Instance.GetMousePosition());

            if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, unitLayerMask))
            {
                Unit targetUnit = hit.collider.GetComponent<Unit>();
                if (targetUnit != null)
                {
                    return CommandType.Move;
                }
            }

            return CommandType.Move;
        }

        private void IssueMoveCommand(List<ISelectable> selectedUnits, Vector3 targetPosition, bool isQueueCommand, bool breakPatrol = true)
        {
            List<Unit> units = GetUnitsFromSelection(selectedUnits);
            if (units.Count == 0) return;

            // Break any existing patrol routes for these units (normal movement overrides patrol)
            if (breakPatrol && waypointManager != null)
            {
                waypointManager.BreakPatrolRoutes(units);
            }

            if (enableWaypointCommands && waypointManager != null)
            {
                // Use waypoint system
                waypointManager.CreateWaypoint(units, targetPosition, isQueueCommand);
            }
            else if (enableFormationCommands && pathfindingManager != null && units.Count > 1)
            {
                // Use formation movement
                pathfindingManager.MoveUnitsInFormation(units, targetPosition, currentFormation);
            }
            else
            {
                // Direct movement
                foreach (Unit unit in units)
                {
                    if (unit.CanMove)
                    {
                        unit.MoveTo(targetPosition);
                    }
                }
            }
        }

        private void IssueMoveCommandWithoutBreakingPatrol(List<ISelectable> selectedUnits, Vector3 targetPosition, bool isQueueCommand)
        {
            IssueMoveCommand(selectedUnits, targetPosition, isQueueCommand, false);
        }

        private void IssueAttackCommand(List<ISelectable> selectedUnits, Vector3 targetPosition, bool isQueueCommand)
        {
            // Break patrol routes (attack commands override patrol)
            List<Unit> units = GetUnitsFromSelection(selectedUnits);
            if (waypointManager != null)
            {
                waypointManager.BreakPatrolRoutes(units);
            }

            // Check if there's a specific target at the position
            Unit targetUnit = GetUnitAtPosition(targetPosition);

            foreach (Unit unit in units)
            {
                if (unit == null) continue;

                if (targetUnit != null)
                {
                    // Direct attack on specific target
                    IssueDirectAttackCommand(unit, targetUnit, isQueueCommand);
                }
                else
                {
                    // Attack-move to position
                    IssueAttackMoveCommand(unit, targetPosition, isQueueCommand);
                }
            }

            // Show visual feedback
            ShowAttackCommandFeedback(targetPosition, targetUnit != null);
        }

        private void IssueDirectAttackCommand(Unit unit, Unit target, bool isQueueCommand)
        {
            if (unit == null || target == null) return;

            // Set the unit to attack the specific target
            unit.SetAttackTarget(target);

            // Move to optimal attack range if not already in range
            float distanceToTarget = Vector3.Distance(unit.transform.position, target.transform.position);
            if (distanceToTarget > unit.GetWeaponRange())
            {
                Vector3 attackPosition = CalculateOptimalAttackPosition(unit, target);
                unit.MoveTo(attackPosition);
            }

            Debug.Log($"Unit {unit.name} ordered to attack {target.name}");
        }

        private void IssueAttackMoveCommand(Unit unit, Vector3 targetPosition, bool isQueueCommand)
        {
            if (unit == null) return;

            // Set unit to attack-move mode
            unit.SetAttackMoveTarget(targetPosition);
            unit.MoveTo(targetPosition);

            Debug.Log($"Unit {unit.name} ordered to attack-move to {targetPosition}");
        }

        private Vector3 CalculateOptimalAttackPosition(Unit attacker, Unit target)
        {
            if (attacker == null || target == null) return attacker.transform.position;

            Vector3 directionToTarget = (target.transform.position - attacker.transform.position).normalized;
            float optimalRange = attacker.GetWeaponRange() * 0.8f; // Stay at 80% of max range

            Vector3 optimalPosition = target.transform.position - directionToTarget * optimalRange;

            // Ensure position is valid for pathfinding
            if (PathfindingManager.Instance != null)
            {
                optimalPosition = PathfindingManager.Instance.GetNearestValidPosition(optimalPosition);
            }

            return optimalPosition;
        }

        private Unit GetUnitAtPosition(Vector3 position)
        {
            // Raycast to find unit at position
            Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
            if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, unitLayerMask))
            {
                return hit.collider.GetComponent<Unit>();
            }
            return null;
        }

        private void ShowAttackCommandFeedback(Vector3 position, bool hasTarget)
        {
            if (moveCommandIndicator != null)
            {
                GameObject indicator = Instantiate(moveCommandIndicator, position, Quaternion.identity);

                // Change color based on command type
                Renderer renderer = indicator.GetComponent<Renderer>();
                if (renderer != null)
                {
                    renderer.material.color = hasTarget ? attackCommandColor : Color.yellow; // Yellow for attack-move
                }

                Destroy(indicator, commandIndicatorDuration);
            }
        }





        private void IssueStopCommand()
        {
            if (RTSInputManager.Instance == null || !RTSInputManager.Instance.HasSelection) return;

            var selectedUnits = GetUnitsFromSelection(RTSInputManager.Instance.SelectedUnits);

            foreach (Unit unit in selectedUnits)
            {
                unit.StopMovement();

                if (waypointManager != null)
                {
                    waypointManager.ClearWaypoints(unit);
                }
            }
        }

        private void IssueHoldPositionCommand()
        {
            if (RTSInputManager.Instance == null || !RTSInputManager.Instance.HasSelection) return;

            var selectedUnits = GetUnitsFromSelection(RTSInputManager.Instance.SelectedUnits);

            foreach (Unit unit in selectedUnits)
            {
                unit.StopMovement();
            }
        }

        private void CycleFormationType()
        {
            // Cycle through formation types
            FormationType[] allFormations = { FormationType.None, FormationType.Auto, FormationType.Line, FormationType.Column, FormationType.Wedge, FormationType.Box };
            FormationType oldFormation = currentFormation;
            int currentIndex = (int)currentFormation;
            int nextIndex = (currentIndex + 1) % allFormations.Length;
            currentFormation = (FormationType)nextIndex;

            // Optional debug for formation changes (non-spammy)
            if (DebugManager.Instance != null)
            {
                DebugManager.Instance.LogSystem("Formation", $"Changed to: {currentFormation}");
            }
        }

        private List<Unit> GetUnitsFromSelection(List<ISelectable> selectedUnits)
        {
            List<Unit> units = new List<Unit>();
            foreach (var selectable in selectedUnits)
            {
                if (selectable is Unit unit)
                {
                    units.Add(unit);
                }
            }
            return units;
        }



        private void ShowCommandIndicator(Vector3 position, CommandType commandType)
        {
            if (moveCommandIndicator != null)
            {
                GameObject indicator = Instantiate(moveCommandIndicator, position, Quaternion.identity);

                // Set color based on command type
                var renderer = indicator.GetComponent<Renderer>();
                if (renderer != null)
                {
                    Color indicatorColor = commandType == CommandType.Attack ? attackCommandColor : moveCommandColor;
                    renderer.material.color = indicatorColor;
                }

                // Destroy after duration
                Destroy(indicator, commandIndicatorDuration);
            }
        }









        private void OnDestroy()
        {
            if (RTSInputManager.Instance != null)
            {
                RTSInputManager.Instance.OnRightClickCommand -= HandleRightClickCommand;
                RTSInputManager.Instance.OnStopCommand -= IssueStopCommand;
                RTSInputManager.Instance.OnHoldPositionCommand -= IssueHoldPositionCommand;
                RTSInputManager.Instance.OnFormationToggleCommand -= CycleFormationType;
            }
        }

        // Public API for external systems
        public void SetCommandMode(CommandMode mode)
        {
            currentCommandMode = mode;
            OnCommandModeChanged?.Invoke(mode);
        }

        public void SetFormationType(FormationType formation)
        {
            currentFormation = formation;
        }

        public CommandMode GetCurrentCommandMode()
        {
            return currentCommandMode;
        }

        public FormationType GetCurrentFormationType()
        {
            return currentFormation;
        }
    }

    /// <summary>
    /// Types of commands that can be issued
    /// </summary>
    public enum CommandType
    {
        Move,
        Attack,
        Follow,
        Patrol,
        Hold,
        Stop
    }

    /// <summary>
    /// Command modes for the RTS system
    /// </summary>
    public enum CommandMode
    {
        Move,
        Attack,
        Patrol,
        Formation
    }
}
