using UnityEngine;
using System.Collections.Generic;
using ColdVor.RTS;

namespace ColdVor.AI.BehaviorTree
{
    /// <summary>
    /// Shared context for behavior tree execution
    /// Contains all the data and state needed for intelligent decision making
    /// </summary>
    public class BehaviorContext
    {
        // Unit references
        public Unit Unit { get; private set; }
        public Transform Transform { get; private set; }

        // Tactical situation
        public Unit CurrentTarget { get; set; }
        public Vector3 LastKnownEnemyPosition { get; set; }
        public Vector3 PatrolDestination { get; set; }
        public Vector3 HomePosition { get; set; }

        // Timing
        public float LastEnemyContactTime { get; set; }
        public float LastMovementTime { get; set; }
        public float DecisionCooldown { get; set; }

        // State flags
        public bool IsInCombat { get; set; }
        public bool IsRetreating { get; set; }
        public bool HasCalledReinforcements { get; set; }

        // Cached calculations (updated each frame)
        public List<Unit> NearbyEnemies { get; private set; }
        public List<Unit> NearbyAllies { get; private set; }
        public float HealthPercentage { get; private set; }
        public float DistanceToTarget { get; private set; }
        public bool IsTargetInWeaponRange { get; private set; }
        public bool IsTargetInDetectionRange { get; private set; }

        public BehaviorContext(Unit unit)
        {
            Unit = unit;
            Transform = unit.transform;
            HomePosition = Transform.position;
            
            NearbyEnemies = new List<Unit>();
            NearbyAllies = new List<Unit>();
            
            Reset();
        }

        /// <summary>
        /// Update context with current situation - called each frame
        /// </summary>
        public void UpdateContext()
        {
            // Update health
            HealthPercentage = (float)Unit.CurrentHealth / Unit.MaxHealth;

            // Update enemy list
            NearbyEnemies.Clear();
            NearbyEnemies.AddRange(Unit.DetectedEnemies);

            // Update target information
            if (CurrentTarget != null && !CurrentTarget.IsDead)
            {
                DistanceToTarget = Vector3.Distance(Transform.position, CurrentTarget.transform.position);
                IsTargetInWeaponRange = DistanceToTarget <= Unit.WeaponRange;
                IsTargetInDetectionRange = DistanceToTarget <= Unit.DetectionRange;
                LastKnownEnemyPosition = CurrentTarget.transform.position;
            }
            else
            {
                // Find new target if current one is invalid
                CurrentTarget = FindBestTarget();
                if (CurrentTarget != null)
                {
                    DistanceToTarget = Vector3.Distance(Transform.position, CurrentTarget.transform.position);
                    IsTargetInWeaponRange = DistanceToTarget <= Unit.WeaponRange;
                    IsTargetInDetectionRange = DistanceToTarget <= Unit.DetectionRange;
                    LastKnownEnemyPosition = CurrentTarget.transform.position;
                }
                else
                {
                    DistanceToTarget = float.MaxValue;
                    IsTargetInWeaponRange = false;
                    IsTargetInDetectionRange = false;
                }
            }

            // Update combat state
            IsInCombat = CurrentTarget != null && IsTargetInWeaponRange;

            // Update cooldowns
            if (DecisionCooldown > 0)
                DecisionCooldown -= Time.deltaTime;
        }

        /// <summary>
        /// Find the best target from detected enemies
        /// </summary>
        private Unit FindBestTarget()
        {
            if (NearbyEnemies.Count == 0) return null;

            Unit bestTarget = null;
            float bestScore = float.MinValue;

            foreach (Unit enemy in NearbyEnemies)
            {
                if (enemy == null || enemy.IsDead) continue;

                float distance = Vector3.Distance(Transform.position, enemy.transform.position);
                float healthPercent = (float)enemy.CurrentHealth / enemy.MaxHealth;
                
                // Scoring: prefer closer, weaker enemies
                float score = (100f - distance) + (100f - healthPercent * 100f);
                
                if (score > bestScore)
                {
                    bestScore = score;
                    bestTarget = enemy;
                }
            }

            return bestTarget;
        }

        /// <summary>
        /// Set target and update Unit's combat system
        /// </summary>
        public void SetTarget(Unit target)
        {
            CurrentTarget = target;
            Unit.SetTarget(target);
            
            if (target != null)
            {
                LastEnemyContactTime = Time.time;
                LastKnownEnemyPosition = target.transform.position;
            }
        }

        /// <summary>
        /// Reset context to initial state
        /// </summary>
        public void Reset()
        {
            CurrentTarget = null;
            LastKnownEnemyPosition = Vector3.zero;
            PatrolDestination = Vector3.zero;
            LastEnemyContactTime = 0f;
            LastMovementTime = 0f;
            DecisionCooldown = 0f;
            IsInCombat = false;
            IsRetreating = false;
            HasCalledReinforcements = false;
        }
    }
}
